import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, CreditCard, Check, AlertCircle } from 'lucide-react';
import { usePaddle } from '@/contexts/PaddleContext';
import { toast } from 'sonner';
import { createCheckoutRequestWithRetry, logCheckoutError } from '@/utils/checkout-helpers';
import { PricingPlan } from '@/types';

interface PaddleCheckoutProps {
    plan: PricingPlan;
    billingCycle: 'month' | 'year';
    onSuccess?: (transactionId: string) => void;
    onError?: (error: string) => void;
    disabled?: boolean;
}

export function PaddleCheckout({ 
    plan, 
    billingCycle,  
    onError, 
    disabled = false 
}: PaddleCheckoutProps) {
    const [isLoading, setIsLoading] = useState(false);
    const { isConfigured, isLoaded, error: paddleError } = usePaddle();

    const handleCheckout = async () => {
        if (!isConfigured || !isLoaded) {
            const errorMsg = paddleError || 'Paddle is not available';

            // Show more helpful message for development mode
            if (errorMsg.includes('placeholder') || errorMsg.includes('Development')) {
                toast.info('Development Mode: Paddle checkout will use mock data. Configure real Paddle credentials for testing.', {
                    duration: 5000,
                });
            } else {
                toast.error(errorMsg);
                onError?.(errorMsg);
                return;
            }
        }

        if (!plan.supports_online_payment) {
            const errorMsg = 'Online payments are not available for this plan';
            toast.error(errorMsg);
            onError?.(errorMsg);
            return;
        }

        if (!plan.supports_monthly && billingCycle === 'month') {
            const errorMsg = 'This plan does not support monthly billing';
            toast.error(errorMsg);
            onError?.(errorMsg);
            return;
        }

        if (!plan.supports_yearly && billingCycle === 'year') {
            const errorMsg = 'This plan does not support yearly billing';
            toast.error(errorMsg);
            onError?.(errorMsg);
            return;
        }

        setIsLoading(true);

        try {
            // Use the utility function for checkout request with retry logic
            const data = await createCheckoutRequestWithRetry(
                '/paddle/checkout',
                plan.id,
                billingCycle,
                'Paddle'
            );

            // Check if this is a development mode response
            if (data.development_mode) {
                toast.info('Development Mode: Redirecting to mock checkout page', {
                    duration: 3000,
                });
            }

            // Redirect to checkout (either real Paddle or mock page)
            window.location.href = data.checkout_url;
        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : 'Failed to start checkout';
            logCheckoutError('Paddle', error);

            // Show more helpful error message for development mode
            if (errorMsg.includes('Development Mode:')) {
                toast.error('Development Mode: Please configure real Paddle sandbox credentials for testing.', {
                    duration: 8000,
                });
            } else {
                toast.error(errorMsg);
            }

            onError?.(errorMsg);
        } finally {
            setIsLoading(false);
        }
    };

    const getPriceDisplay = () => {
        if (billingCycle === 'year') {
            const monthlyPrice = plan.price / 12;
            return (
                <div>
                    <span className="text-3xl font-bold">${plan.price}</span>
                    <span className="text-gray-600 ml-1">/year</span>
                    <div className="text-sm text-gray-500">
                        ${monthlyPrice.toFixed(2)}/month billed annually
                    </div>
                </div>
            );
        }
        return (
            <div>
                <span className="text-3xl font-bold">${plan.price}</span>
                <span className="text-gray-600 ml-1">/month</span>
            </div>
        );
    };

    const getSavingsDisplay = () => {
        if (billingCycle === 'year') {
            const monthlyCost = plan.price * 12;
            const yearlyCost = plan.price;
            const savings = monthlyCost - yearlyCost;
            const savingsPercent = Math.round((savings / monthlyCost) * 100);
            
            return (
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                    Save ${savings} ({savingsPercent}%)
                </Badge>
            );
        }
        return null;
    };

    if (!isLoaded) {
        return (
            <Card className="w-full max-w-md">
                <CardContent className="flex items-center justify-center p-6">
                    <Loader2 className="h-6 w-6 animate-spin" />
                    <span className="ml-2">Loading payment options...</span>
                </CardContent>
            </Card>
        );
    }

    if (paddleError || !isConfigured) {
        return (
            <Card className="w-full max-w-md border-red-200">
                <CardContent className="flex items-center justify-center p-6 text-red-600">
                    <AlertCircle className="h-6 w-6" />
                    <span className="ml-2">Payment system unavailable</span>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className="w-full max-w-md">
            <CardHeader className="text-center">
                <CardTitle className="flex items-center justify-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    {plan.display_name}
                </CardTitle>
                <CardDescription>{plan.description}</CardDescription>
                <div className="mt-4">
                    {getPriceDisplay()}
                    {getSavingsDisplay()}
                </div>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="space-y-2">
                    {plan.features.map((feature, index) => (
                        <div key={index} className="flex items-center gap-2">
                            <Check className="h-4 w-4 text-green-600" />
                            <span className="text-sm">{feature}</span>
                        </div>
                    ))}
                </div>
                
                <Button
                    onClick={handleCheckout}
                    disabled={disabled || isLoading || !plan.supports_online_payment}
                    className="w-full"
                    size="lg"
                >
                    {isLoading ? (
                        <>
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            Processing...
                        </>
                    ) : (
                        <>
                            <CreditCard className="h-4 w-4 mr-2" />
                            Subscribe Now
                        </>
                    )}
                </Button>

                {!plan.supports_online_payment && (
                    <p className="text-sm text-gray-500 text-center">
                        Online payments not available for this plan
                    </p>
                )}
            </CardContent>
        </Card>
    );
}
