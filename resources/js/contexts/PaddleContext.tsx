import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';

interface PaddleConfig {
    client_token: string;
    environment: 'sandbox' | 'production';
    currency: string;
    development_mode?: boolean;
    mock_mode?: boolean;
    note?: string;
}

interface PaddleEvent {
    name: string;
    data: {
        transaction_id?: string;
        [key: string]: unknown;
    };
}

interface PaddleCheckoutOptions {
    items?: Array<{
        priceId: string;
        quantity?: number;
    }>;
    customer?: {
        email?: string;
        [key: string]: unknown;
    };
    customData?: Record<string, unknown>;
    [key: string]: unknown;
}

interface PaddlePricePreviewOptions {
    items: Array<{
        priceId: string;
        quantity?: number;
    }>;
    customerIpAddress?: string;
    address?: {
        countryCode: string;
        postalCode?: string;
    };
    [key: string]: unknown;
}

interface PaddleInstance {
    Initialize: (config: {
        token: string;
        eventCallback?: (event: PaddleEvent) => void;
    }) => void;
    Checkout: {
        open: (options: PaddleCheckoutOptions) => void;
    };
    PricePreview: (options: PaddlePricePreviewOptions) => Promise<unknown>;
    Environment: {
        set: (environment: 'sandbox' | 'production') => void;
    };
}

interface PaddleContextType {
    isLoaded: boolean;
    isConfigured: boolean;
    config: PaddleConfig | null;
    error: string | null;
    paddle: PaddleInstance | null;
    initializePaddle: () => Promise<void>;
    openCheckout: (options: PaddleCheckoutOptions) => void;
    pricePreview: (options: PaddlePricePreviewOptions) => Promise<unknown>;
}

const PaddleContext = createContext<PaddleContextType | undefined>(undefined);

interface PaddleProviderProps {
    children: React.ReactNode;
}

export function PaddleProvider({ children }: PaddleProviderProps) {
    const [isLoaded, setIsLoaded] = useState(false);
    const [isConfigured, setIsConfigured] = useState(false);
    const [config, setConfig] = useState<PaddleConfig | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [paddle, setPaddle] = useState<PaddleInstance | null>(null);

    // Helper function to detect placeholder values
    const isPlaceholderValue = (value: string): boolean => {
        // Empty values are considered placeholders
        if (!value || value.trim().length === 0) {
            return true;
        }

        const lowerValue = value.toLowerCase();

        // Check for obvious placeholder patterns (but not real Paddle test tokens)
        const placeholderPatterns = [
            'placeholder',
            'demo_',
            'example_',
            'your_',
            'replace_',
            'test_key',
            'test_token',
            'test_secret',
        ];

        if (placeholderPatterns.some(pattern => lowerValue.includes(pattern))) {
            return true;
        }

        // Check for generic test_ patterns that are too short or simple (but allow real Paddle tokens)
        if (/^test_[a-z0-9]{1,10}$/i.test(value)) {
            return true;
        }

        // Check for very short values that are likely placeholders
        if (value.length < 10) {
            return true;
        }

        return false;
    };

    const fetchConfig = async (): Promise<PaddleConfig | null> => {
        try {
            const response = await fetch('/paddle/config', {
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                // If Paddle is not configured, that's okay - just return null
                if (response.status === 503) {
                    console.info('Paddle is not configured - payment features will be disabled');
                    return null;
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const config = await response.json();

            // Handle development mode responses
            if (config.error && config.development_mode) {
                console.warn('Paddle Development Mode:', config.error);
                console.info('Help:', config.help);
                return null; // Don't try to initialize Paddle with placeholder credentials
            }

            return config;
        } catch (err) {
            console.warn('Paddle configuration not available:', err);
            return null;
        }
    };

    const initializePaddle = useCallback(async () => {
        try {
            setError(null);

            // Check if Paddle script is loaded
            if (typeof window.Paddle === 'undefined') {
                console.info('Paddle.js script not loaded - payment features will be disabled');
                setIsConfigured(false);
                setIsLoaded(true);
                return;
            }

            // Fetch configuration
            const paddleConfig = await fetchConfig();
            if (!paddleConfig) {
                console.info('Paddle not configured - payment features will be disabled');
                setIsConfigured(false);
                setIsLoaded(true);
                return;
            }

            setConfig(paddleConfig);

            // Check for development mode
            if (paddleConfig.development_mode || paddleConfig.mock_mode) {
                console.info('Paddle Development Mode detected - Paddle SDK will not be initialized');
                console.info('Note:', paddleConfig.note || 'Using placeholder credentials');
                setIsConfigured(false);
                setIsLoaded(true);
                return;
            }

            // Check if client token is a placeholder
            if (isPlaceholderValue(paddleConfig.client_token)) {
                console.warn('Paddle client token appears to be a placeholder - Paddle SDK will not be initialized');
                setIsConfigured(false);
                setIsLoaded(true);
                return;
            }

            // Set environment
            if (paddleConfig.environment === 'production') {
                window.Paddle.Environment.set('production');
            } else {
                window.Paddle.Environment.set('sandbox');
            }

            // Initialize Paddle
            window.Paddle.Initialize({
                token: paddleConfig.client_token,
                eventCallback: (event: PaddleEvent) => {
                    console.log('Paddle event:', event);

                    // Handle checkout events
                    if (event.name === 'checkout.completed') {
                        // Redirect to success page or handle completion
                        window.location.href = '/subscription/paddle/success?_ptxn=' + event.data.transaction_id;
                    } else if (event.name === 'checkout.closed') {
                        // Handle checkout cancellation
                        console.log('Checkout closed');
                    }
                }
            });

            setPaddle(window.Paddle);
            setIsConfigured(true);
            setIsLoaded(true);
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to initialize Paddle';
            setError(errorMessage);
            setIsConfigured(false);
            setIsLoaded(true);
            console.warn('Paddle initialization failed:', err);
        }
    }, []);

    const openCheckout = (options: PaddleCheckoutOptions) => {
        if (!paddle || !isConfigured) {
            console.error('Paddle not initialized');
            return;
        }

        try {
            paddle.Checkout.open(options);
        } catch (err) {
            console.error('Failed to open Paddle checkout:', err);
        }
    };

    const pricePreview = async (options: PaddlePricePreviewOptions) => {
        if (!paddle || !isConfigured) {
            throw new Error('Paddle not initialized');
        }

        try {
            return await paddle.PricePreview(options);
        } catch (err) {
            console.error('Failed to get price preview:', err);
            throw err;
        }
    };

    useEffect(() => {
        // Initialize Paddle when component mounts
        const timer = setTimeout(() => {
            initializePaddle();
        }, 100); // Small delay to ensure Paddle script is loaded

        return () => clearTimeout(timer);
    }, [initializePaddle]);

    const value: PaddleContextType = {
        isLoaded,
        isConfigured,
        config,
        error,
        paddle,
        initializePaddle,
        openCheckout,
        pricePreview,
    };

    return (
        <PaddleContext.Provider value={value}>
            {children}
        </PaddleContext.Provider>
    );
}

export function usePaddle() {
    const context = useContext(PaddleContext);
    if (context === undefined) {
        throw new Error('usePaddle must be used within a PaddleProvider');
    }
    return context;
}

// Extend window object to include Paddle
declare global {
    interface Window {
        Paddle: PaddleInstance;
    }
}
