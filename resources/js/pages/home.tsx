import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { useState, useEffect } from 'react';

interface SearchStatus {
    has_searched: boolean;
    message: string;
}
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
    Search,
    Smartphone,
    Database,
    Shield,
    Zap,
    Users,
    ArrowRight,
    Clock,
    Globe
} from 'lucide-react';

export default function Home() {
    const { auth } = usePage<SharedData>().props;
    const [searchQuery, setSearchQuery] = useState('');
    const [deviceId, setDeviceId] = useState('');
    const [searchStatus, setSearchStatus] = useState<SearchStatus | null>(null);
    const [isLoading, setIsLoading] = useState(false);

    // Generate device ID on mount
    useEffect(() => {
        const generateDeviceId = () => {
            let id = localStorage.getItem('mobile_parts_device_id');
            if (!id) {
                id = 'device_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
                localStorage.setItem('mobile_parts_device_id', id);
            }
            return id;
        };

        const id = generateDeviceId();
        setDeviceId(id);

        // Check search status
        if (id && !auth.user) {
            fetch(`/guest/search/status?device_id=${id}`)
                .then(res => res.json())
                .then(data => setSearchStatus(data))
                .catch(() => {});
        }
    }, [auth.user]);

    return (
        <>
            <Head title="Mobile Parts Database - Find Compatible Parts for Any Device">
                <meta name="description" content="Search our comprehensive database of mobile phone parts. Find compatible components for repairs, replacements, and upgrades. Free search available." />
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>

            {/* Navigation */}
            <nav className="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200 dark:bg-gray-900/80 dark:border-gray-800">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center space-x-2">
                            <Smartphone className="h-8 w-8 text-blue-600" />
                            <span className="text-xl font-bold text-gray-900 dark:text-white">
                                FixHaat
                            </span>
                        </div>
                        <div className="flex items-center space-x-4">
                            {auth.user ? (
                                <Link href={route('dashboard')}>
                                    <Button variant="outline" size="sm">
                                        Dashboard
                                    </Button>
                                </Link>
                            ) : (
                                <>
                                    <Link href={route('login')}>
                                        <Button variant="ghost" size="sm" className="text-gray-700 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white font-medium">
                                            Log in
                                        </Button>
                                    </Link>
                                    <Link href={route('register')}>
                                        <Button size="sm">
                                            Sign up
                                        </Button>
                                    </Link>
                                </>
                            )}
                        </div>
                    </div>
                </div>
            </nav>

            {/* Main Content */}
            <main className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">

                {/* Hero Section */}
                <section className="relative px-4 pt-16 pb-12 sm:px-6 lg:px-8 lg:pt-24 lg:pb-16">
                    <div className="max-w-7xl mx-auto">
                        <div className="text-center">
                            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl md:text-6xl dark:text-white">
                                Find the Right <span className="text-blue-600">Mobile Parts</span>
                                <span className="text-gray-600 dark:text-gray-300 text-2xl sm:text-3xl md:text-4xl font-normal block mt-2">
                                    for Any Device
                                </span>
                            </h1>
                            <p className="mt-6 max-w-2xl mx-auto text-lg text-gray-600 dark:text-gray-300 sm:text-xl">
                                Search our comprehensive database of mobile phone parts. Find compatible components
                                for repairs, replacements, and upgrades with detailed specifications and compatibility information.
                            </p>

                            {/* Search Status for Guests */}
                            {!auth.user && searchStatus && (
                                <div className="mt-6 max-w-md mx-auto">
                                    <Badge
                                        variant={searchStatus.has_searched ? "destructive" : "default"}
                                        className="text-sm px-3 py-1"
                                    >
                                        {searchStatus.message}
                                    </Badge>
                                </div>
                            )}
                        </div>
                    </div>
                </section>

                {/* Search Section */}
                <section className="relative px-4 pb-16 sm:px-6 lg:px-8 -mt-8">
                    <div className="max-w-4xl mx-auto">
                        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm dark:bg-gray-800/80">
                            <CardHeader className="text-center pb-6">
                                <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
                                    Start Your Search
                                </CardTitle>
                                <CardDescription className="text-lg">
                                    {auth.user
                                        ? "Search unlimited parts in our database"
                                        : "Get 1 free search to explore our database"
                                    }
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <SearchInterface
                                    searchQuery={searchQuery}
                                    setSearchQuery={setSearchQuery}
                                    deviceId={deviceId}
                                    isAuthenticated={!!auth.user}
                                    searchStatus={searchStatus}
                                    isLoading={isLoading}
                                    setIsLoading={setIsLoading}
                                />
                            </CardContent>
                        </Card>
                    </div>
                </section>

                {/* Features Section */}
                <section className="px-4 py-16 sm:px-6 lg:px-8 bg-white dark:bg-gray-800">
                    <div className="max-w-7xl mx-auto">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white sm:text-4xl">
                                Why Choose FixHaat?
                            </h2>
                            <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
                                The most comprehensive mobile parts database for professionals and enthusiasts
                            </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                            <FeatureCard
                                icon={<Database className="h-8 w-8 text-blue-600" />}
                                title="Comprehensive Database"
                                description="300+ mobile models with detailed parts information and specifications"
                            />
                            <FeatureCard
                                icon={<Shield className="h-8 w-8 text-green-600" />}
                                title="Verified Compatibility"
                                description="All part compatibility information is verified and regularly updated"
                            />
                            <FeatureCard
                                icon={<Zap className="h-8 w-8 text-yellow-600" />}
                                title="Fast Search"
                                description="Lightning-fast search results with advanced filtering options"
                            />
                            <FeatureCard
                                icon={<Users className="h-8 w-8 text-purple-600" />}
                                title="Professional Grade"
                                description="Trusted by repair shops, technicians, and parts suppliers worldwide"
                            />
                            <FeatureCard
                                icon={<Clock className="h-8 w-8 text-red-600" />}
                                title="Always Updated"
                                description="Regular updates with new models and parts as they become available"
                            />
                            <FeatureCard
                                icon={<Globe className="h-8 w-8 text-indigo-600" />}
                                title="Global Coverage"
                                description="Parts information for devices from all major manufacturers worldwide"
                            />
                        </div>
                    </div>
                </section>

                {/* CTA Section */}
                {!auth.user && (
                    <section className="px-4 py-16 sm:px-6 lg:px-8 bg-blue-600">
                        <div className="max-w-4xl mx-auto text-center">
                            <h2 className="text-3xl font-bold text-white sm:text-4xl">
                                Ready to Get Started?
                            </h2>
                            <p className="mt-4 text-xl text-blue-100">
                                Sign up now for unlimited access to our mobile parts database
                            </p>
                            <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                                <Link href={route('register')}>
                                    <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                                        Start Free Trial
                                        <ArrowRight className="ml-2 h-5 w-5" />
                                    </Button>
                                </Link>
                                <Link href={route('login')}>
                                    <Button size="lg" variant="outline" className="border-2 border-white text-blue-600 hover:bg-white hover:text-blue-400 font-semibold">
                                        Sign In
                                    </Button>
                                </Link>
                            </div>
                        </div>
                    </section>
                )}

                {/* Footer */}
                <footer className="bg-gray-900 text-white py-12">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <div className="flex items-center justify-center space-x-2 mb-4">
                                <Smartphone className="h-8 w-8 text-blue-400" />
                                <span className="text-2xl font-bold">FixHaat</span>
                            </div>
                            <p className="text-gray-400">
                                The comprehensive mobile parts database for professionals
                            </p>
                            <div className="mt-8 flex justify-center space-x-6">
                                <a href="#" className="text-gray-400 hover:text-white">Privacy</a>
                                <a href="#" className="text-gray-400 hover:text-white">Terms</a>
                                <a href="#" className="text-gray-400 hover:text-white">Support</a>
                            </div>
                        </div>
                    </div>
                </footer>
            </main>
        </>
    );
}

// Search Interface Component
function SearchInterface({
    searchQuery,
    setSearchQuery,
    deviceId,
    isAuthenticated,
    searchStatus,
    isLoading,
    setIsLoading
}: {
    searchQuery: string;
    setSearchQuery: (query: string) => void;
    deviceId: string;
    isAuthenticated: boolean;
    searchStatus: SearchStatus | null;
    isLoading: boolean;
    setIsLoading: (loading: boolean) => void;
}) {
    const [searchType, setSearchType] = useState('all');
    const [showLimitModal, setShowLimitModal] = useState(false);

    const handleSearch = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!searchQuery.trim()) return;

        // Check if guest user has already searched
        if (!isAuthenticated && searchStatus?.has_searched) {
            setShowLimitModal(true);
            return;
        }

        setIsLoading(true);

        try {
            const url = isAuthenticated
                ? `/search/results?q=${encodeURIComponent(searchQuery)}&type=${searchType}`
                : `/guest/search?q=${encodeURIComponent(searchQuery)}&type=${searchType}&device_id=${deviceId}`;

            window.location.href = url;
        } catch (error) {
            console.error('Search error:', error);
            setIsLoading(false);
        }
    };

    const canSearch = isAuthenticated || !searchStatus?.has_searched;

    return (
        <>
            <form onSubmit={handleSearch} className="space-y-4">
                <div className="flex flex-col sm:flex-row gap-3">
                    <div className="flex-1">
                        <Input
                            type="text"
                            placeholder="Search for parts, models, or brands..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="h-12 text-lg"
                            disabled={isLoading}
                        />
                    </div>
                    <select
                        value={searchType}
                        onChange={(e) => setSearchType(e.target.value)}
                        className="h-12 px-3 border border-gray-300 rounded-md bg-white dark:bg-gray-700 dark:border-gray-600"
                        disabled={isLoading}
                    >
                        <option value="all">All</option>
                        <option value="part_name">Parts</option>
                        <option value="model">Models</option>
                        <option value="category">Categories</option>
                    </select>
                    <Button
                        type="submit"
                        size="lg"
                        className="h-12 px-8"
                        disabled={!canSearch || isLoading || !searchQuery.trim()}
                    >
                        {isLoading ? (
                            <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                Searching...
                            </>
                        ) : (
                            <>
                                <Search className="w-5 h-5 mr-2" />
                                Search
                            </>
                        )}
                    </Button>
                </div>

                {!isAuthenticated && (
                    <div className="text-center text-sm text-gray-600 dark:text-gray-400">
                        {searchStatus?.has_searched ? (
                            <span className="text-red-600 dark:text-red-400">
                                You've used your free search. <Link href={route('register')} className="underline">Sign up</Link> for unlimited access.
                            </span>
                        ) : (
                            <span>
                                Guest users get 1 search. <Link href={route('register')} className="underline">Sign up</Link> for unlimited access.
                            </span>
                        )}
                    </div>
                )}
            </form>

            {/* Search Limit Modal */}
            {showLimitModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full">
                        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
                            Search Limit Reached
                        </h3>
                        <p className="text-gray-600 dark:text-gray-300 mb-6">
                            You've used your free search for today. Sign up to get unlimited access to our mobile parts database.
                        </p>
                        <div className="flex gap-3">
                            <Link href={route('register')} className="flex-1">
                                <Button className="w-full">
                                    Sign Up Now
                                </Button>
                            </Link>
                            <Button
                                variant="outline"
                                onClick={() => setShowLimitModal(false)}
                                className="flex-1"
                            >
                                Close
                            </Button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
}

// Feature Card Component
function FeatureCard({ icon, title, description }: {
    icon: React.ReactNode;
    title: string;
    description: string;
}) {
    return (
        <Card className="text-center p-6 hover:shadow-lg transition-shadow">
            <CardContent className="space-y-4">
                <div className="flex justify-center">
                    {icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                    {description}
                </p>
            </CardContent>
        </Card>
    );
}
