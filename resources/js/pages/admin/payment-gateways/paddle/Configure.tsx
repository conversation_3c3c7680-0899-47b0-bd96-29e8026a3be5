import { Head, Link, useForm } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, Save, ExternalLink, CheckCircle, Info, Eye, EyeOff, TestTube, RefreshCw, AlertTriangle, Copy } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';

interface PaddleConfig {
    id?: number;
    is_enabled: boolean;
    environment: 'sandbox' | 'production';
    api_key: string;
    client_token: string;
    webhook_secret: string;
    vendor_id: string;
    default_currency: string;
    supported_currencies: string[];
    webhook_url: string;
    logging_enabled: boolean;
    debug_mode: boolean;
}

interface ConnectionStatus {
    success: boolean;
    message: string;
    details?: any;
}

interface Props {
    paddleConfig?: PaddleConfig;
    webhookUrl?: string;
    connectionStatus?: ConnectionStatus;
    isConfigured?: boolean;
}

export default function Configure({
    paddleConfig: propConfig,
    webhookUrl: propWebhookUrl,
    connectionStatus,
    isConfigured = false
}: Props) {
    const [showApiKey, setShowApiKey] = useState(false);
    const [showClientToken, setShowClientToken] = useState(false);
    const [showWebhookSecret, setShowWebhookSecret] = useState(false);
    const [testingConnection, setTestingConnection] = useState(false);
    const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
    const [debugLogs, setDebugLogs] = useState<string[]>([]);
    const [errorReports, setErrorReports] = useState<Array<{
        id: string;
        timestamp: string;
        type: 'error' | 'warning' | 'info';
        source: string;
        message: string;
        details?: any;
    }>>([]);
    const [showErrorDetails, setShowErrorDetails] = useState<string | null>(null);

    // Default configuration
    const defaultConfig: PaddleConfig = {
        is_enabled: false,
        environment: 'sandbox',
        api_key: '',
        client_token: '',
        webhook_secret: '',
        vendor_id: '',
        default_currency: 'USD',
        supported_currencies: ['USD', 'EUR', 'GBP'],
        webhook_url: `${window.location.origin}/webhooks/paddle`,
        logging_enabled: true,
        debug_mode: false,
    };

    const paddleConfig = propConfig || defaultConfig;
    const webhookUrl = propWebhookUrl || paddleConfig.webhook_url;

    const { data, setData, post, processing, errors, recentlySuccessful } = useForm({
        is_enabled: paddleConfig.is_enabled,
        environment: paddleConfig.environment,
        api_key: paddleConfig.api_key,
        client_token: paddleConfig.client_token,
        webhook_secret: paddleConfig.webhook_secret,
        vendor_id: paddleConfig.vendor_id,
        default_currency: paddleConfig.default_currency,
        supported_currencies: paddleConfig.supported_currencies,
        logging_enabled: paddleConfig.logging_enabled,
        debug_mode: paddleConfig.debug_mode,
    });

    const logDebug = (message: string, data?: any) => {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = `[${timestamp}] ${message}${data ? ': ' + JSON.stringify(data, null, 2) : ''}`;
        setDebugLogs(prev => [...prev.slice(-9), logEntry]);

        if (paddleConfig.debug_mode) {
            console.log('[Paddle Debug]', message, data);
        }
    };

    const addErrorReport = (type: 'error' | 'warning' | 'info', source: string, message: string, details?: any) => {
        const errorReport = {
            id: Date.now().toString(),
            timestamp: new Date().toISOString(),
            type,
            source,
            message,
            details
        };
        setErrorReports(prev => [errorReport, ...prev.slice(0, 19)]); // Keep last 20 reports
        logDebug(`${type.toUpperCase()}: ${source} - ${message}`, details);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        logDebug('Form submission started', data);

        post(route('admin.payment-gateways.paddle.store'), {
            onSuccess: () => {
                logDebug('Configuration saved successfully');
                addErrorReport('info', 'Configuration Save', 'Paddle configuration saved successfully');
                setTestResult({ success: true, message: 'Configuration saved successfully!' });
            },
            onError: (errors) => {
                logDebug('Configuration save failed', errors);
                addErrorReport('error', 'Configuration Save', 'Failed to save configuration', errors);
                setTestResult({ success: false, message: 'Failed to save configuration. Please check your settings.' });
            }
        });
    };

    const testConnection = async () => {
        if (!data.api_key || !data.client_token) {
            const errorMsg = 'Please enter both API Key and Client Token first.';
            logDebug('Test connection failed: Missing credentials');
            addErrorReport('warning', 'Connection Test', errorMsg);
            setTestResult({ success: false, message: errorMsg });
            return;
        }

        setTestingConnection(true);
        logDebug('Testing Paddle connection', {
            environment: data.environment,
            has_api_key: !!data.api_key,
            has_client_token: !!data.client_token
        });

        try {
            const response = await fetch(route('admin.payment-gateways.paddle.test'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    api_key: data.api_key,
                    client_token: data.client_token,
                    environment: data.environment,
                }),
            });

            const result = await response.json();
            logDebug('Connection test result', result);

            if (result.success) {
                addErrorReport('info', 'Connection Test', 'Paddle API connection successful', result);
            } else {
                addErrorReport('error', 'Connection Test', result.message || 'Connection failed', result);
            }

            setTestResult({
                success: result.success,
                message: result.message || (result.success ? 'Connection successful!' : 'Connection failed')
            });
        } catch (error) {
            const errorMsg = 'Failed to test connection. Please try again.';
            logDebug('Connection test error', error);
            addErrorReport('error', 'Connection Test', errorMsg, error);
            setTestResult({
                success: false,
                message: errorMsg
            });
        } finally {
            setTestingConnection(false);
        }
    };

    const copyWebhookUrl = () => {
        navigator.clipboard.writeText(webhookUrl);
        toast.success('Webhook URL copied to clipboard');
    };

    const configurationComplete = data.api_key && data.client_token && data.webhook_secret;

    // Initialize with some diagnostic information
    useEffect(() => {
        // Add initial diagnostic info
        addErrorReport('info', 'System Startup', 'Paddle configuration page loaded successfully');

        // Check for potential configuration issues
        if (!data.api_key) {
            addErrorReport('warning', 'Configuration Check', 'API Key is not configured');
        }
        if (!data.client_token) {
            addErrorReport('warning', 'Configuration Check', 'Client Token is not configured');
        }
        if (!data.webhook_secret) {
            addErrorReport('warning', 'Configuration Check', 'Webhook Secret is not configured');
        }

        // Check environment
        if (data.environment === 'sandbox') {
            addErrorReport('info', 'Environment Check', 'Running in sandbox mode - safe for testing');
        }
    }, []); // Only run once on mount

    return (
        <AppLayout>
            <Head title="Configure Paddle Payment Gateway" />
            
            <div className="flex h-full flex-1 flex-col p-4 space-y-6">
                {/* Header */}
                <div className="flex items-center gap-4">
                    <Link href={route('admin.payment-gateways.index')}>
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back to Payment Gateways
                        </Button>
                    </Link>
                    <div className="flex items-center gap-3">
                        <img 
                            src="/images/paddle-logo.png" 
                            alt="Paddle logo"
                            className="w-8 h-8 object-contain"
                            onError={(e) => {
                                e.currentTarget.style.display = 'none';
                            }}
                        />
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">Paddle Configuration</h1>
                            <p className="text-muted-foreground mt-2">
                                Configure Paddle payment gateway settings
                            </p>
                        </div>
                        {isConfigured && (
                            <Badge variant="default" className="bg-green-100 text-green-800">
                                <CheckCircle className="w-3 h-3 mr-1" />
                                Configured
                            </Badge>
                        )}
                    </div>
                </div>

                {/* Success Message */}
                {recentlySuccessful && (
                    <Alert className="bg-green-50 border-green-200">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <AlertDescription className="text-green-800">
                            Paddle configuration has been saved successfully!
                        </AlertDescription>
                    </Alert>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid gap-6 lg:grid-cols-2">
                        {/* Basic Configuration */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Basic Configuration</CardTitle>
                                <CardDescription>
                                    Enable and configure basic Paddle settings
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label>Enable Paddle</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Activate Paddle payment gateway
                                        </p>
                                    </div>
                                    <Switch
                                        checked={data.is_enabled}
                                        onCheckedChange={(checked) => setData('is_enabled', checked)}
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="environment">Environment</Label>
                                    <select
                                        id="environment"
                                        value={data.environment}
                                        onChange={(e) => setData('environment', e.target.value as 'sandbox' | 'production')}
                                        className="w-full px-3 py-2 border border-input rounded-md bg-background"
                                    >
                                        <option value="sandbox">Sandbox (Testing)</option>
                                        <option value="production">Production (Live)</option>
                                    </select>
                                    {errors.environment && <p className="text-sm text-destructive">{errors.environment}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="default_currency">Default Currency</Label>
                                    <select
                                        id="default_currency"
                                        value={data.default_currency}
                                        onChange={(e) => setData('default_currency', e.target.value)}
                                        className="w-full px-3 py-2 border border-input rounded-md bg-background"
                                    >
                                        <option value="USD">USD - US Dollar</option>
                                        <option value="EUR">EUR - Euro</option>
                                        <option value="GBP">GBP - British Pound</option>
                                    </select>
                                    {errors.default_currency && <p className="text-sm text-destructive">{errors.default_currency}</p>}
                                </div>

                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label>Enable Logging</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Log Paddle API requests and responses
                                        </p>
                                    </div>
                                    <Switch
                                        checked={data.logging_enabled}
                                        onCheckedChange={(checked) => setData('logging_enabled', checked)}
                                    />
                                </div>

                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label>Debug Mode</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Enable detailed debugging and real-time logs
                                        </p>
                                    </div>
                                    <Switch
                                        checked={data.debug_mode}
                                        onCheckedChange={(checked) => setData('debug_mode', checked)}
                                    />
                                </div>
                            </CardContent>
                        </Card>

                        {/* API Credentials */}
                        <Card>
                            <CardHeader>
                                <CardTitle>API Credentials</CardTitle>
                                <CardDescription>
                                    Enter your Paddle API credentials from your dashboard
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="api_key">API Key</Label>
                                    <div className="relative">
                                        <Input
                                            id="api_key"
                                            type={showApiKey ? "text" : "password"}
                                            value={data.api_key}
                                            onChange={(e) => setData('api_key', e.target.value)}
                                            placeholder="pdl_sdbx_apikey_..."
                                            required
                                        />
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            size="sm"
                                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                            onClick={() => setShowApiKey(!showApiKey)}
                                        >
                                            {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                        </Button>
                                    </div>
                                    <p className="text-xs text-muted-foreground">
                                        Server-side API key from your Paddle dashboard (Developer Tools → Authentication)
                                    </p>
                                    {errors.api_key && <p className="text-sm text-destructive">{errors.api_key}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="client_token">Client Token</Label>
                                    <div className="relative">
                                        <Input
                                            id="client_token"
                                            type={showClientToken ? "text" : "password"}
                                            value={data.client_token}
                                            onChange={(e) => setData('client_token', e.target.value)}
                                            placeholder="live_... or test_..."
                                            required
                                        />
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            size="sm"
                                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                            onClick={() => setShowClientToken(!showClientToken)}
                                        >
                                            {showClientToken ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                        </Button>
                                    </div>
                                    <p className="text-xs text-muted-foreground">
                                        Client-side token for Paddle.js (Developer Tools → Authentication)
                                    </p>
                                    {errors.client_token && <p className="text-sm text-destructive">{errors.client_token}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="vendor_id">Vendor ID (Optional)</Label>
                                    <Input
                                        id="vendor_id"
                                        value={data.vendor_id}
                                        onChange={(e) => setData('vendor_id', e.target.value)}
                                        placeholder="12345"
                                    />
                                    <p className="text-xs text-muted-foreground">
                                        Your Paddle vendor ID (used for some legacy operations)
                                    </p>
                                    {errors.vendor_id && <p className="text-sm text-destructive">{errors.vendor_id}</p>}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Webhook Configuration */}
                        <Card className="lg:col-span-2">
                            <CardHeader>
                                <CardTitle>Webhook Configuration</CardTitle>
                                <CardDescription>
                                    Configure webhooks to receive payment notifications
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="webhook_url">Webhook URL</Label>
                                    <div className="flex gap-2">
                                        <Input
                                            id="webhook_url"
                                            value={webhookUrl}
                                            readOnly
                                            className="bg-muted"
                                        />
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={copyWebhookUrl}
                                        >
                                            <Copy className="mr-2 h-4 w-4" />
                                            Copy
                                        </Button>
                                    </div>
                                    <p className="text-xs text-muted-foreground">
                                        Add this URL to your Paddle webhook settings
                                    </p>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="webhook_secret">Webhook Secret</Label>
                                    <div className="relative">
                                        <Input
                                            id="webhook_secret"
                                            type={showWebhookSecret ? "text" : "password"}
                                            value={data.webhook_secret}
                                            onChange={(e) => setData('webhook_secret', e.target.value)}
                                            placeholder="pdl_ntfset_..."
                                        />
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            size="sm"
                                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                            onClick={() => setShowWebhookSecret(!showWebhookSecret)}
                                        >
                                            {showWebhookSecret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                        </Button>
                                    </div>
                                    <p className="text-xs text-muted-foreground">
                                        Secret key for webhook verification (optional but recommended)
                                    </p>
                                    {errors.webhook_secret && <p className="text-sm text-destructive">{errors.webhook_secret}</p>}
                                </div>

                                <Alert>
                                    <Info className="h-4 w-4" />
                                    <AlertDescription>
                                        Configure this webhook URL in your Paddle dashboard to receive payment notifications.
                                        <Link href="https://developer.paddle.com/webhooks" target="_blank" className="ml-1 text-blue-600 hover:underline">
                                            Learn more <ExternalLink className="inline w-3 h-3" />
                                        </Link>
                                    </AlertDescription>
                                </Alert>
                            </CardContent>
                        </Card>

                        {/* Connection Testing */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <TestTube className="h-5 w-5" />
                                    Connection Testing
                                </CardTitle>
                                <CardDescription>
                                    Test your Paddle API connection and credentials
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center gap-4">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={testConnection}
                                        disabled={testingConnection || !data.api_key || !data.client_token}
                                    >
                                        {testingConnection ? (
                                            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                                        ) : (
                                            <TestTube className="mr-2 h-4 w-4" />
                                        )}
                                        {testingConnection ? 'Testing...' : 'Test Connection'}
                                    </Button>

                                    {configurationComplete ? (
                                        <Badge className="bg-primary/10 text-primary border-primary/20">
                                            <CheckCircle className="w-3 h-3 mr-1" />
                                            Configuration Complete
                                        </Badge>
                                    ) : (
                                        <Badge variant="outline" className="text-muted-foreground">
                                            <AlertTriangle className="w-3 h-3 mr-1" />
                                            Configuration Incomplete
                                        </Badge>
                                    )}
                                </div>

                                {testResult && (
                                    <Alert className={testResult.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
                                        {testResult.success ? (
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                        ) : (
                                            <AlertTriangle className="h-4 w-4 text-red-600" />
                                        )}
                                        <AlertDescription className={testResult.success ? "text-green-800" : "text-red-800"}>
                                            {testResult.message}
                                        </AlertDescription>
                                    </Alert>
                                )}
                            </CardContent>
                        </Card>

                        {/* Debug Logging */}
                        {data.debug_mode && debugLogs.length > 0 && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Info className="h-5 w-5" />
                                        Debug Logs
                                    </CardTitle>
                                    <CardDescription>
                                        Real-time debugging information
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="bg-muted p-4 rounded-md max-h-64 overflow-y-auto">
                                        <pre className="text-xs text-muted-foreground whitespace-pre-wrap">
                                            {debugLogs.join('\n')}
                                        </pre>
                                    </div>
                                </CardContent>
                            </Card>
                        )}

                        {/* Error Reports */}
                        {errorReports.length > 0 && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <AlertTriangle className="h-5 w-5" />
                                        Error Reports & Diagnostics
                                    </CardTitle>
                                    <CardDescription>
                                        System errors, warnings, and diagnostic information
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3 max-h-80 overflow-y-auto">
                                        {errorReports.map((report) => (
                                            <div
                                                key={report.id}
                                                className={`p-3 rounded-md border ${
                                                    report.type === 'error'
                                                        ? 'border-red-200 bg-red-50'
                                                        : report.type === 'warning'
                                                        ? 'border-yellow-200 bg-yellow-50'
                                                        : 'border-blue-200 bg-blue-50'
                                                }`}
                                            >
                                                <div className="flex items-start justify-between">
                                                    <div className="flex-1">
                                                        <div className="flex items-center gap-2 mb-1">
                                                            {report.type === 'error' && (
                                                                <AlertTriangle className="h-4 w-4 text-red-600" />
                                                            )}
                                                            {report.type === 'warning' && (
                                                                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                                                            )}
                                                            {report.type === 'info' && (
                                                                <Info className="h-4 w-4 text-blue-600" />
                                                            )}
                                                            <span className={`text-sm font-medium ${
                                                                report.type === 'error'
                                                                    ? 'text-red-800'
                                                                    : report.type === 'warning'
                                                                    ? 'text-yellow-800'
                                                                    : 'text-blue-800'
                                                            }`}>
                                                                {report.source}
                                                            </span>
                                                            <span className="text-xs text-muted-foreground">
                                                                {new Date(report.timestamp).toLocaleString()}
                                                            </span>
                                                        </div>
                                                        <p className={`text-sm ${
                                                            report.type === 'error'
                                                                ? 'text-red-700'
                                                                : report.type === 'warning'
                                                                ? 'text-yellow-700'
                                                                : 'text-blue-700'
                                                        }`}>
                                                            {report.message}
                                                        </p>
                                                        {report.details && (
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                className="mt-2 h-6 px-2 text-xs"
                                                                onClick={() => setShowErrorDetails(
                                                                    showErrorDetails === report.id ? null : report.id
                                                                )}
                                                            >
                                                                {showErrorDetails === report.id ? 'Hide Details' : 'Show Details'}
                                                            </Button>
                                                        )}
                                                        {showErrorDetails === report.id && report.details && (
                                                            <div className="mt-2 p-2 bg-muted rounded text-xs">
                                                                <pre className="whitespace-pre-wrap text-muted-foreground">
                                                                    {JSON.stringify(report.details, null, 2)}
                                                                </pre>
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                    <div className="mt-4 pt-4 border-t">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => setErrorReports([])}
                                            className="text-xs"
                                        >
                                            Clear All Reports
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-4">
                        <Button type="submit" disabled={processing}>
                            <Save className="mr-2 h-4 w-4" />
                            {processing ? 'Saving...' : 'Save Configuration'}
                        </Button>
                        <Link href={route('admin.payment-gateways.index')}>
                            <Button variant="outline">Cancel</Button>
                        </Link>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
