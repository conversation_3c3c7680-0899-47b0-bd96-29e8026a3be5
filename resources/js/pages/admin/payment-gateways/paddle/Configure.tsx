import { Head, Link, useForm } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, Save, ExternalLink, CheckCircle, Info } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { getPaddleConfig } from '../data';

interface PaddleConfig {
    id?: number;
    is_enabled: boolean;
    environment: 'sandbox' | 'production';
    vendor_id: string;
    vendor_auth_code: string;
    public_key: string;
    webhook_secret: string;
    default_currency: string;
    supported_currencies: string[];
    webhook_url: string;
}

interface Props {
    paddleConfig?: PaddleConfig;
    webhookUrl?: string;
}

export default function Configure({ paddleConfig: propConfig, webhookUrl: propWebhookUrl }: Props) {
    // Use prop data if available, otherwise use sample data
    const paddleConfig = propConfig || getPaddleConfig();
    const webhookUrl = propWebhookUrl || `${window.location.origin}/webhooks/paddle`;
    const { data, setData, post, processing, errors, recentlySuccessful } = useForm({
        is_enabled: paddleConfig.is_enabled || false,
        environment: paddleConfig.environment || 'sandbox',
        vendor_id: paddleConfig.vendor_id || '',
        vendor_auth_code: paddleConfig.vendor_auth_code || '',
        public_key: paddleConfig.public_key || '',
        webhook_secret: paddleConfig.webhook_secret || '',
        default_currency: paddleConfig.default_currency || 'USD',
        supported_currencies: paddleConfig.supported_currencies || ['USD', 'EUR', 'GBP'],
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.payment-gateways.paddle.store'), {
            onSuccess: () => {
                // Success handled by redirect or flash message
            },
        });
    };

    const isConfigured = data.vendor_id && data.vendor_auth_code && data.public_key;

    return (
        <AppLayout>
            <Head title="Configure Paddle Payment Gateway" />
            
            <div className="flex h-full flex-1 flex-col p-4 space-y-6">
                {/* Header */}
                <div className="flex items-center gap-4">
                    <Link href={route('admin.payment-gateways.index')}>
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back to Payment Gateways
                        </Button>
                    </Link>
                    <div className="flex items-center gap-3">
                        <img 
                            src="/images/paddle-logo.png" 
                            alt="Paddle logo"
                            className="w-8 h-8 object-contain"
                            onError={(e) => {
                                e.currentTarget.style.display = 'none';
                            }}
                        />
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">Paddle Configuration</h1>
                            <p className="text-muted-foreground mt-2">
                                Configure Paddle payment gateway settings
                            </p>
                        </div>
                        {isConfigured && (
                            <Badge variant="default" className="bg-green-100 text-green-800">
                                <CheckCircle className="w-3 h-3 mr-1" />
                                Configured
                            </Badge>
                        )}
                    </div>
                </div>

                {/* Success Message */}
                {recentlySuccessful && (
                    <Alert className="bg-green-50 border-green-200">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <AlertDescription className="text-green-800">
                            Paddle configuration has been saved successfully!
                        </AlertDescription>
                    </Alert>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid gap-6 lg:grid-cols-2">
                        {/* Basic Configuration */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Basic Configuration</CardTitle>
                                <CardDescription>
                                    Enable and configure basic Paddle settings
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label>Enable Paddle</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Activate Paddle payment gateway
                                        </p>
                                    </div>
                                    <Switch
                                        checked={data.is_enabled}
                                        onCheckedChange={(checked) => setData('is_enabled', checked)}
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="environment">Environment</Label>
                                    <select
                                        id="environment"
                                        value={data.environment}
                                        onChange={(e) => setData('environment', e.target.value as 'sandbox' | 'production')}
                                        className="w-full px-3 py-2 border border-input rounded-md bg-background"
                                    >
                                        <option value="sandbox">Sandbox (Testing)</option>
                                        <option value="production">Production (Live)</option>
                                    </select>
                                    {errors.environment && <p className="text-sm text-destructive">{errors.environment}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="default_currency">Default Currency</Label>
                                    <select
                                        id="default_currency"
                                        value={data.default_currency}
                                        onChange={(e) => setData('default_currency', e.target.value)}
                                        className="w-full px-3 py-2 border border-input rounded-md bg-background"
                                    >
                                        <option value="USD">USD - US Dollar</option>
                                        <option value="EUR">EUR - Euro</option>
                                        <option value="GBP">GBP - British Pound</option>
                                    </select>
                                    {errors.default_currency && <p className="text-sm text-destructive">{errors.default_currency}</p>}
                                </div>
                            </CardContent>
                        </Card>

                        {/* API Credentials */}
                        <Card>
                            <CardHeader>
                                <CardTitle>API Credentials</CardTitle>
                                <CardDescription>
                                    Enter your Paddle API credentials from your dashboard
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="vendor_id">Vendor ID</Label>
                                    <Input
                                        id="vendor_id"
                                        value={data.vendor_id}
                                        onChange={(e) => setData('vendor_id', e.target.value)}
                                        placeholder="12345"
                                        required
                                    />
                                    <p className="text-xs text-muted-foreground">
                                        Your Paddle vendor ID from the dashboard
                                    </p>
                                    {errors.vendor_id && <p className="text-sm text-destructive">{errors.vendor_id}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="vendor_auth_code">Vendor Auth Code</Label>
                                    <Input
                                        id="vendor_auth_code"
                                        type="password"
                                        value={data.vendor_auth_code}
                                        onChange={(e) => setData('vendor_auth_code', e.target.value)}
                                        placeholder="Enter your vendor auth code"
                                        required
                                    />
                                    <p className="text-xs text-muted-foreground">
                                        API authentication code from Paddle
                                    </p>
                                    {errors.vendor_auth_code && <p className="text-sm text-destructive">{errors.vendor_auth_code}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="public_key">Public Key</Label>
                                    <Input
                                        id="public_key"
                                        value={data.public_key}
                                        onChange={(e) => setData('public_key', e.target.value)}
                                        placeholder="Enter your public key"
                                        required
                                    />
                                    <p className="text-xs text-muted-foreground">
                                        Public key for webhook verification
                                    </p>
                                    {errors.public_key && <p className="text-sm text-destructive">{errors.public_key}</p>}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Webhook Configuration */}
                        <Card className="lg:col-span-2">
                            <CardHeader>
                                <CardTitle>Webhook Configuration</CardTitle>
                                <CardDescription>
                                    Configure webhooks to receive payment notifications
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="webhook_url">Webhook URL</Label>
                                    <div className="flex gap-2">
                                        <Input
                                            id="webhook_url"
                                            value={webhookUrl}
                                            readOnly
                                            className="bg-muted"
                                        />
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={() => navigator.clipboard.writeText(webhookUrl)}
                                        >
                                            Copy
                                        </Button>
                                    </div>
                                    <p className="text-xs text-muted-foreground">
                                        Add this URL to your Paddle webhook settings
                                    </p>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="webhook_secret">Webhook Secret</Label>
                                    <Input
                                        id="webhook_secret"
                                        type="password"
                                        value={data.webhook_secret}
                                        onChange={(e) => setData('webhook_secret', e.target.value)}
                                        placeholder="Enter webhook secret"
                                    />
                                    <p className="text-xs text-muted-foreground">
                                        Secret key for webhook verification (optional but recommended)
                                    </p>
                                    {errors.webhook_secret && <p className="text-sm text-destructive">{errors.webhook_secret}</p>}
                                </div>

                                <Alert>
                                    <Info className="h-4 w-4" />
                                    <AlertDescription>
                                        Configure this webhook URL in your Paddle dashboard to receive payment notifications.
                                        <Link href="https://developer.paddle.com/webhooks" target="_blank" className="ml-1 text-blue-600 hover:underline">
                                            Learn more <ExternalLink className="inline w-3 h-3" />
                                        </Link>
                                    </AlertDescription>
                                </Alert>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-4">
                        <Button type="submit" disabled={processing}>
                            <Save className="mr-2 h-4 w-4" />
                            {processing ? 'Saving...' : 'Save Configuration'}
                        </Button>
                        <Link href={route('admin.payment-gateways.index')}>
                            <Button variant="outline">Cancel</Button>
                        </Link>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
