import { Head, Link, useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Save, Plus, X } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';
import { toast } from 'sonner';

interface Category {
    id: number;
    name: string;
    description: string | null;
    parent_id: number | null;
    is_active: boolean;
}

interface PartFormData {
    category_id: string;
    name: string;
    part_number: string;
    manufacturer: string;
    description: string;
    specifications: Record<string, string>;
    images: string[];
    is_active: boolean;
    [key: string]: string | boolean | string[] | Record<string, string>;
}

interface Props {
    categories: Category[];
}

export default function Create({ categories }: Props) {
    const { data, setData, post, processing, errors } = useForm<PartFormData>({
        category_id: '',
        name: '',
        part_number: '',
        manufacturer: '',
        description: '',
        specifications: {} as Record<string, string>,
        images: [] as string[],
        is_active: true,
    });

    const [newSpecKey, setNewSpecKey] = useState('');
    const [newSpecValue, setNewSpecValue] = useState('');
    const [newImageUrl, setNewImageUrl] = useState('');

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/admin/parts', {
            onSuccess: () => {
                toast.success(`Part "${data.name}" has been created successfully.`);
            },
            onError: () => {
                toast.error('Failed to create part. Please check the form and try again.');
            }
        });
    };

    const addSpecification = () => {
        if (newSpecKey && newSpecValue) {
            setData('specifications', {
                ...data.specifications,
                [newSpecKey]: newSpecValue
            });
            setNewSpecKey('');
            setNewSpecValue('');
        }
    };

    const removeSpecification = (key: string) => {
        const newSpecs = { ...data.specifications };
        delete newSpecs[key];
        setData('specifications', newSpecs);
    };

    const addImage = () => {
        if (newImageUrl) {
            setData('images', [...data.images, newImageUrl]);
            setNewImageUrl('');
        }
    };

    const removeImage = (index: number) => {
        const newImages = data.images.filter((_, i) => i !== index);
        setData('images', newImages);
    };

    return (
        <AppLayout>
            <Head title="Create Part - Admin" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center gap-4">
                    <Link href="/admin/parts">
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Back to Parts
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Create Part</h1>
                        <p className="text-muted-foreground">
                            Add a new mobile device part
                        </p>
                    </div>
                </div>

                {/* Form */}
                <Card>
                    <CardHeader>
                        <CardTitle>Part Details</CardTitle>
                        <CardDescription>
                            Enter the information for the new part
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Category Selection */}
                            <div className="space-y-2">
                                <Label htmlFor="category_id">Category *</Label>
                                <Select value={data.category_id} onValueChange={(value) => setData('category_id', value)}>
                                    <SelectTrigger className={errors.category_id ? 'border-red-500' : ''}>
                                        <SelectValue placeholder="Select a category" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {categories.map((category) => (
                                            <SelectItem key={category.id} value={category.id.toString()}>
                                                {category.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                {errors.category_id && (
                                    <p className="text-sm text-red-600">{errors.category_id}</p>
                                )}
                            </div>

                            {/* Part Name */}
                            <div className="space-y-2">
                                <Label htmlFor="name">Part Name *</Label>
                                <Input
                                    id="name"
                                    type="text"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    placeholder="e.g., LCD Display Assembly, Battery, Camera Module"
                                    className={errors.name ? 'border-red-500' : ''}
                                />
                                {errors.name && (
                                    <p className="text-sm text-red-600">{errors.name}</p>
                                )}
                            </div>

                            {/* Part Number */}
                            <div className="space-y-2">
                                <Label htmlFor="part_number">Part Number</Label>
                                <Input
                                    id="part_number"
                                    type="text"
                                    value={data.part_number}
                                    onChange={(e) => setData('part_number', e.target.value)}
                                    placeholder="e.g., LCD-001, BAT-123, CAM-456"
                                    className={errors.part_number ? 'border-red-500' : ''}
                                />
                                {errors.part_number && (
                                    <p className="text-sm text-red-600">{errors.part_number}</p>
                                )}
                            </div>

                            {/* Manufacturer */}
                            <div className="space-y-2">
                                <Label htmlFor="manufacturer">Manufacturer</Label>
                                <Input
                                    id="manufacturer"
                                    type="text"
                                    value={data.manufacturer}
                                    onChange={(e) => setData('manufacturer', e.target.value)}
                                    placeholder="e.g., Samsung, LG, Sony, Foxconn"
                                    className={errors.manufacturer ? 'border-red-500' : ''}
                                />
                                {errors.manufacturer && (
                                    <p className="text-sm text-red-600">{errors.manufacturer}</p>
                                )}
                            </div>

                            {/* Description */}
                            <div className="space-y-2">
                                <Label htmlFor="description">Description</Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    placeholder="Detailed description of the part, its features, and compatibility notes..."
                                    className={errors.description ? 'border-red-500' : ''}
                                    rows={4}
                                />
                                {errors.description && (
                                    <p className="text-sm text-red-600">{errors.description}</p>
                                )}
                            </div>

                            {/* Specifications */}
                            <div className="space-y-4">
                                <Label>Specifications</Label>
                                <div className="space-y-3">
                                    {Object.entries(data.specifications).map(([key, value]) => (
                                        <div key={key} className="flex items-center gap-2 p-3 border rounded-lg">
                                            <div className="flex-1">
                                                <span className="font-medium text-sm">{key}:</span>
                                                <span className="ml-2 text-sm">{value}</span>
                                            </div>
                                            <Button
                                                type="button"
                                                variant="outline"
                                                size="sm"
                                                onClick={() => removeSpecification(key)}
                                                className="text-destructive hover:text-destructive"
                                            >
                                                <X className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    ))}

                                    <div className="flex gap-2">
                                        <Input
                                            placeholder="Specification name (e.g., Material, Voltage)"
                                            value={newSpecKey}
                                            onChange={(e) => setNewSpecKey(e.target.value)}
                                            className="flex-1"
                                        />
                                        <Input
                                            placeholder="Value (e.g., Glass, 3.7V)"
                                            value={newSpecValue}
                                            onChange={(e) => setNewSpecValue(e.target.value)}
                                            className="flex-1"
                                        />
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={addSpecification}
                                            disabled={!newSpecKey || !newSpecValue}
                                        >
                                            <Plus className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                                {errors.specifications && (
                                    <p className="text-sm text-red-600">{errors.specifications}</p>
                                )}
                            </div>

                            {/* Images */}
                            <div className="space-y-4">
                                <Label>Images</Label>
                                <div className="space-y-3">
                                    {data.images.map((image, index) => (
                                        <div key={index} className="flex items-center gap-2 p-3 border rounded-lg">
                                            <div className="flex-1">
                                                <img
                                                    src={image}
                                                    alt={`Part image ${index + 1}`}
                                                    className="w-16 h-16 object-cover rounded"
                                                    onError={(e) => {
                                                        e.currentTarget.style.display = 'none';
                                                    }}
                                                />
                                                <p className="text-sm text-muted-foreground mt-1 break-all">{image}</p>
                                            </div>
                                            <Button
                                                type="button"
                                                variant="outline"
                                                size="sm"
                                                onClick={() => removeImage(index)}
                                                className="text-destructive hover:text-destructive"
                                            >
                                                <X className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    ))}

                                    <div className="flex gap-2">
                                        <Input
                                            placeholder="Image URL"
                                            value={newImageUrl}
                                            onChange={(e) => setNewImageUrl(e.target.value)}
                                            className="flex-1"
                                        />
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={addImage}
                                            disabled={!newImageUrl}
                                        >
                                            <Plus className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                                {errors.images && (
                                    <p className="text-sm text-red-600">{errors.images}</p>
                                )}
                            </div>

                            {/* Active Status */}
                            <div className="flex items-center space-x-2">
                                <Switch
                                    id="is_active"
                                    checked={data.is_active}
                                    onCheckedChange={(checked) => setData('is_active', checked)}
                                />
                                <Label htmlFor="is_active">Active</Label>
                            </div>

                            {/* Submit Buttons */}
                            <div className="flex justify-end gap-4 pt-6 border-t">
                                <Link href="/admin/parts">
                                    <Button variant="outline" type="button">
                                        Cancel
                                    </Button>
                                </Link>
                                <Button type="submit" disabled={processing}>
                                    <Save className="w-4 h-4 mr-2" />
                                    {processing ? 'Creating...' : 'Create Part'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
                </div>
            </div>
        </AppLayout>
    );
}
