import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
    Users,
    Package,
    Search,
    TrendingUp,
    Eye,
    ShoppingCart,
    Calendar,
    Activity,
    BarChart3,
    PieChart,
    LineChart,
    Database,
    Server,
    HardDrive,
    Zap,
    AlertTriangle,
    CheckCircle,
    Clock,
    Target
} from 'lucide-react';
import {
    <PERSON><PERSON><PERSON> as RechartsLine<PERSON>hart,
    <PERSON><PERSON>hart,
    <PERSON><PERSON>hart,
    <PERSON><PERSON><PERSON> as RechartsPie<PERSON>hart,
    Cell,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    Legend,
    ResponsiveContainer,
    Area,
    Bar,
    Line,
    Pie
} from 'recharts';
import AppLayout from '@/layouts/app-layout';
import { chartTheme, isDarkMode, getTooltipStyle, getGridStyle, getAxisStyle, getLegendStyle, logChartTheme } from '@/lib/chart-theme';
import { useEffect, useState } from 'react';

interface DashboardStats {
    total_users: number;
    premium_users: number;
    free_users: number;
    total_parts: number;
    total_categories: number;
    total_brands: number;
    total_models: number;
    total_searches_today: number;
    total_searches_week: number;
    total_searches_month: number;
    active_subscriptions: number;
    recent_registrations: number;
    avg_searches_per_user: number;
}

interface ChartDataPoint {
    date: string;
    [key: string]: string | number;
}

interface DistributionData {
    name: string;
    value: number;
}

interface RecentSearch {
    id: number;
    query: string;
    type: string;
    results: number;
    user: {
        name: string;
        email: string;
        plan: string;
    } | null;
    created_at: string;
}

interface RecentUser {
    id: number;
    name: string;
    email: string;
    plan: string;
    created_at: string;
    searches_count: number;
}

interface TopCategory {
    id: number;
    name: string;
    parts_count: number;
}

interface TopBrand {
    id: number;
    name: string;
    models_count: number;
}

interface SystemHealth {
    database: {
        status: string;
        response_time: number | null;
        message: string;
    };
    cache: {
        status: string;
        message: string;
    };
    storage: {
        status: string;
        usage_percent?: number;
        free_space?: string;
        total_space?: string;
        message: string;
    };
}

interface Props {
    stats: DashboardStats;
    charts: {
        userRegistrationTrends: ChartDataPoint[];
        searchActivityTrends: ChartDataPoint[];
        userDistribution: DistributionData[];
        categoryDistribution: DistributionData[];
        brandDistribution: DistributionData[];
        performanceMetrics: ChartDataPoint[];
    };
    recentActivity: {
        searches: RecentSearch[];
        users: RecentUser[];
    };
    topPerformers: {
        categories: TopCategory[];
        brands: TopBrand[];
    };
    systemHealth: SystemHealth;
}

export default function Dashboard({ stats, charts, recentActivity, topPerformers, systemHealth }: Props) {
    const [darkMode, setDarkMode] = useState(false);

    useEffect(() => {
        // Detect dark mode and set up observer
        const updateDarkMode = () => {
            const isDark = isDarkMode();
            setDarkMode(isDark);

            // Log chart theme for debugging
            if (process.env.NODE_ENV === 'development') {
                logChartTheme('dashboard', isDark);
            }
        };

        updateDarkMode();

        // Watch for theme changes
        const observer = new MutationObserver(updateDarkMode);
        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['class']
        });

        return () => observer.disconnect();
    }, []);

    // Custom tooltip component for charts
    const CustomTooltip = ({ active, payload, label }: any) => {
        if (active && payload && payload.length) {
            return (
                <div style={getTooltipStyle(darkMode)}>
                    <p className="font-medium">{label}</p>
                    {payload.map((entry: any, index: number) => (
                        <p key={index} style={{ color: entry.color }}>
                            {entry.name}: {entry.value}
                        </p>
                    ))}
                </div>
            );
        }
        return null;
    };

    return (
        <AppLayout>
            <Head title="Admin Dashboard" />

            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-6 overflow-x-auto">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight text-foreground">Admin Dashboard</h1>
                        <p className="text-muted-foreground mt-2">
                            Comprehensive overview of your mobile parts database with advanced analytics
                        </p>
                    </div>
                    <div className="hidden md:flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                            <Activity className="h-3 w-3 mr-1" />
                            Live Data
                        </Badge>
                        <Badge variant="secondary" className="text-xs">
                            <BarChart3 className="h-3 w-3 mr-1" />
                            Advanced Analytics
                        </Badge>
                    </div>
                </div>

                {/* Enhanced Stats Grid */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6">
                    <Card className="border-l-4 border-l-blue-500">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                            <Users className="h-4 w-4 text-blue-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-foreground">{stats.total_users.toLocaleString()}</div>
                            <p className="text-xs text-muted-foreground">
                                <span className="text-blue-600 font-medium">{stats.premium_users}</span> premium,
                                <span className="text-gray-600 font-medium ml-1">{stats.free_users}</span> free
                            </p>
                        </CardContent>
                    </Card>

                    <Card className="border-l-4 border-l-green-500">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Parts</CardTitle>
                            <Package className="h-4 w-4 text-green-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-foreground">{stats.total_parts.toLocaleString()}</div>
                            <p className="text-xs text-muted-foreground">
                                In {stats.total_categories} categories
                            </p>
                        </CardContent>
                    </Card>

                    <Card className="border-l-4 border-l-purple-500">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Searches Today</CardTitle>
                            <Search className="h-4 w-4 text-purple-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-foreground">{stats.total_searches_today.toLocaleString()}</div>
                            <p className="text-xs text-muted-foreground">
                                <span className="text-purple-600 font-medium">{stats.total_searches_week}</span> this week
                            </p>
                        </CardContent>
                    </Card>

                    <Card className="border-l-4 border-l-orange-500">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
                            <TrendingUp className="h-4 w-4 text-orange-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-foreground">{stats.active_subscriptions.toLocaleString()}</div>
                            <p className="text-xs text-muted-foreground">
                                <span className="text-orange-600 font-medium">{stats.recent_registrations}</span> new this week
                            </p>
                        </CardContent>
                    </Card>

                    <Card className="border-l-4 border-l-teal-500">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Brands & Models</CardTitle>
                            <Eye className="h-4 w-4 text-teal-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-foreground">{stats.total_brands.toLocaleString()}</div>
                            <p className="text-xs text-muted-foreground">
                                <span className="text-teal-600 font-medium">{stats.total_models}</span> models
                            </p>
                        </CardContent>
                    </Card>

                    <Card className="border-l-4 border-l-indigo-500">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Avg Searches</CardTitle>
                            <Target className="h-4 w-4 text-indigo-600" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-foreground">{stats.avg_searches_per_user}</div>
                            <p className="text-xs text-muted-foreground">
                                Per user
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Charts Section */}
                <Tabs defaultValue="analytics" className="space-y-6">
                    <TabsList className="grid w-full grid-cols-4">
                        <TabsTrigger value="analytics">Analytics</TabsTrigger>
                        <TabsTrigger value="performance">Performance</TabsTrigger>
                        <TabsTrigger value="distribution">Distribution</TabsTrigger>
                        <TabsTrigger value="activity">Activity</TabsTrigger>
                    </TabsList>

                    <TabsContent value="analytics" className="space-y-6">
                        <div className="grid gap-6 md:grid-cols-2">
                            {/* User Registration Trends */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <LineChart className="h-5 w-5 text-blue-600" />
                                        User Registration Trends
                                    </CardTitle>
                                    <CardDescription>
                                        Daily user registrations over the last 30 days
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <ResponsiveContainer width="100%" height={300}>
                                        <RechartsLineChart data={charts.userRegistrationTrends}>
                                            <CartesianGrid {...getGridStyle(darkMode)} />
                                            <XAxis
                                                dataKey="date"
                                                {...getAxisStyle(darkMode)}
                                                tick={{ fontSize: 12 }}
                                            />
                                            <YAxis {...getAxisStyle(darkMode)} tick={{ fontSize: 12 }} />
                                            <Tooltip content={<CustomTooltip />} />
                                            <Legend {...getLegendStyle(darkMode)} />
                                            <Line
                                                type="monotone"
                                                dataKey="total"
                                                stroke={chartTheme.line(darkMode).colors[0]}
                                                strokeWidth={2}
                                                name="Total Users"
                                            />
                                            <Line
                                                type="monotone"
                                                dataKey="premium"
                                                stroke={chartTheme.line(darkMode).colors[1]}
                                                strokeWidth={2}
                                                name="Premium Users"
                                            />
                                        </RechartsLineChart>
                                    </ResponsiveContainer>
                                </CardContent>
                            </Card>

                            {/* Search Activity Trends */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <BarChart3 className="h-5 w-5 text-green-600" />
                                        Search Activity Trends
                                    </CardTitle>
                                    <CardDescription>
                                        Daily search activity and user engagement
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <ResponsiveContainer width="100%" height={300}>
                                        <AreaChart data={charts.searchActivityTrends}>
                                            <CartesianGrid {...getGridStyle(darkMode)} />
                                            <XAxis
                                                dataKey="date"
                                                {...getAxisStyle(darkMode)}
                                                tick={{ fontSize: 12 }}
                                            />
                                            <YAxis {...getAxisStyle(darkMode)} tick={{ fontSize: 12 }} />
                                            <Tooltip content={<CustomTooltip />} />
                                            <Legend {...getLegendStyle(darkMode)} />
                                            <Area
                                                type="monotone"
                                                dataKey="searches"
                                                stackId="1"
                                                stroke={chartTheme.area(darkMode).colors[0]}
                                                fill={chartTheme.area(darkMode).colors[0]}
                                                fillOpacity={0.6}
                                                name="Total Searches"
                                            />
                                            <Area
                                                type="monotone"
                                                dataKey="users"
                                                stackId="2"
                                                stroke={chartTheme.area(darkMode).colors[1]}
                                                fill={chartTheme.area(darkMode).colors[1]}
                                                fillOpacity={0.6}
                                                name="Active Users"
                                            />
                                        </AreaChart>
                                    </ResponsiveContainer>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>

                    <TabsContent value="performance" className="space-y-6">
                        <div className="grid gap-6 md:grid-cols-2">
                            {/* Performance Metrics */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Zap className="h-5 w-5 text-yellow-600" />
                                        Search Performance
                                    </CardTitle>
                                    <CardDescription>
                                        Search success rates and average results
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <ResponsiveContainer width="100%" height={300}>
                                        <BarChart data={charts.performanceMetrics}>
                                            <CartesianGrid {...getGridStyle(darkMode)} />
                                            <XAxis
                                                dataKey="date"
                                                {...getAxisStyle(darkMode)}
                                                tick={{ fontSize: 12 }}
                                            />
                                            <YAxis {...getAxisStyle(darkMode)} tick={{ fontSize: 12 }} />
                                            <Tooltip content={<CustomTooltip />} />
                                            <Legend {...getLegendStyle(darkMode)} />
                                            <Bar
                                                dataKey="successRate"
                                                fill={chartTheme.bar(darkMode).colors[0]}
                                                name="Success Rate (%)"
                                            />
                                            <Bar
                                                dataKey="avgResults"
                                                fill={chartTheme.bar(darkMode).colors[1]}
                                                name="Avg Results"
                                            />
                                        </BarChart>
                                    </ResponsiveContainer>
                                </CardContent>
                            </Card>

                            {/* System Health */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Server className="h-5 w-5 text-blue-600" />
                                        System Health
                                    </CardTitle>
                                    <CardDescription>
                                        Real-time system status and performance
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {/* Database Health */}
                                    <div className="flex items-center justify-between p-3 border rounded-lg">
                                        <div className="flex items-center gap-3">
                                            <Database className="h-5 w-5 text-blue-600" />
                                            <div>
                                                <p className="text-sm font-medium">Database</p>
                                                <p className="text-xs text-muted-foreground">
                                                    {systemHealth.database.response_time}ms response time
                                                </p>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            {systemHealth.database.status === 'healthy' ? (
                                                <CheckCircle className="h-4 w-4 text-green-600" />
                                            ) : (
                                                <AlertTriangle className="h-4 w-4 text-red-600" />
                                            )}
                                            <Badge variant={systemHealth.database.status === 'healthy' ? 'default' : 'destructive'}>
                                                {systemHealth.database.status}
                                            </Badge>
                                        </div>
                                    </div>

                                    {/* Cache Health */}
                                    <div className="flex items-center justify-between p-3 border rounded-lg">
                                        <div className="flex items-center gap-3">
                                            <Zap className="h-5 w-5 text-yellow-600" />
                                            <div>
                                                <p className="text-sm font-medium">Cache</p>
                                                <p className="text-xs text-muted-foreground">
                                                    {systemHealth.cache.message}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            {systemHealth.cache.status === 'healthy' ? (
                                                <CheckCircle className="h-4 w-4 text-green-600" />
                                            ) : (
                                                <AlertTriangle className="h-4 w-4 text-red-600" />
                                            )}
                                            <Badge variant={systemHealth.cache.status === 'healthy' ? 'default' : 'destructive'}>
                                                {systemHealth.cache.status}
                                            </Badge>
                                        </div>
                                    </div>

                                    {/* Storage Health */}
                                    <div className="flex items-center justify-between p-3 border rounded-lg">
                                        <div className="flex items-center gap-3">
                                            <HardDrive className="h-5 w-5 text-purple-600" />
                                            <div>
                                                <p className="text-sm font-medium">Storage</p>
                                                <p className="text-xs text-muted-foreground">
                                                    {systemHealth.storage.usage_percent}% used
                                                </p>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            {systemHealth.storage.status === 'healthy' ? (
                                                <CheckCircle className="h-4 w-4 text-green-600" />
                                            ) : (
                                                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                                            )}
                                            <Badge variant={systemHealth.storage.status === 'healthy' ? 'default' : 'secondary'}>
                                                {systemHealth.storage.status}
                                            </Badge>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>

                    <TabsContent value="distribution" className="space-y-6">
                        <div className="grid gap-6 md:grid-cols-3">
                            {/* User Distribution */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <PieChart className="h-5 w-5 text-blue-600" />
                                        User Distribution
                                    </CardTitle>
                                    <CardDescription>
                                        Free vs Premium users
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <ResponsiveContainer width="100%" height={250}>
                                        <RechartsPieChart>
                                            <Pie
                                                data={charts.userDistribution}
                                                cx="50%"
                                                cy="50%"
                                                innerRadius={40}
                                                outerRadius={80}
                                                paddingAngle={5}
                                                dataKey="value"
                                            >
                                                {charts.userDistribution.map((entry, index) => (
                                                    <Cell
                                                        key={`cell-${index}`}
                                                        fill={chartTheme.pie(darkMode).colors[index % chartTheme.pie(darkMode).colors.length]}
                                                    />
                                                ))}
                                            </Pie>
                                            <Tooltip content={<CustomTooltip />} />
                                            <Legend {...getLegendStyle(darkMode)} />
                                        </RechartsPieChart>
                                    </ResponsiveContainer>
                                </CardContent>
                            </Card>

                            {/* Category Distribution */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Package className="h-5 w-5 text-green-600" />
                                        Category Distribution
                                    </CardTitle>
                                    <CardDescription>
                                        Parts by category
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <ResponsiveContainer width="100%" height={250}>
                                        <RechartsPieChart>
                                            <Pie
                                                data={charts.categoryDistribution}
                                                cx="50%"
                                                cy="50%"
                                                innerRadius={40}
                                                outerRadius={80}
                                                paddingAngle={5}
                                                dataKey="value"
                                            >
                                                {charts.categoryDistribution.map((entry, index) => (
                                                    <Cell
                                                        key={`cell-${index}`}
                                                        fill={chartTheme.pie(darkMode).colors[index % chartTheme.pie(darkMode).colors.length]}
                                                    />
                                                ))}
                                            </Pie>
                                            <Tooltip content={<CustomTooltip />} />
                                            <Legend {...getLegendStyle(darkMode)} />
                                        </RechartsPieChart>
                                    </ResponsiveContainer>
                                </CardContent>
                            </Card>

                            {/* Brand Distribution */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Eye className="h-5 w-5 text-purple-600" />
                                        Brand Distribution
                                    </CardTitle>
                                    <CardDescription>
                                        Models by brand
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <ResponsiveContainer width="100%" height={250}>
                                        <RechartsPieChart>
                                            <Pie
                                                data={charts.brandDistribution}
                                                cx="50%"
                                                cy="50%"
                                                innerRadius={40}
                                                outerRadius={80}
                                                paddingAngle={5}
                                                dataKey="value"
                                            >
                                                {charts.brandDistribution.map((entry, index) => (
                                                    <Cell
                                                        key={`cell-${index}`}
                                                        fill={chartTheme.pie(darkMode).colors[index % chartTheme.pie(darkMode).colors.length]}
                                                    />
                                                ))}
                                            </Pie>
                                            <Tooltip content={<CustomTooltip />} />
                                            <Legend {...getLegendStyle(darkMode)} />
                                        </RechartsPieChart>
                                    </ResponsiveContainer>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>

                    <TabsContent value="activity" className="space-y-6">
                        <div className="grid gap-6 md:grid-cols-2">
                            {/* Recent Searches */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Search className="h-5 w-5 text-purple-600" />
                                        Recent Searches
                                    </CardTitle>
                                    <CardDescription>
                                        Latest search activity from users
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {recentActivity.searches.length > 0 ? (
                                            recentActivity.searches.map((search) => (
                                                <div key={search.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-accent/50 transition-colors">
                                                    <div className="space-y-1">
                                                        <p className="text-sm font-medium leading-none text-foreground">
                                                            {search.query}
                                                        </p>
                                                        <p className="text-xs text-muted-foreground">
                                                            by <span className="text-purple-600 font-medium">{search.user?.name || 'Anonymous'}</span> •
                                                            <span className="text-green-600 font-medium ml-1">{search.results}</span> results
                                                        </p>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <Badge variant="secondary" className="text-xs">
                                                            {search.type}
                                                        </Badge>
                                                        <Badge variant={search.user?.plan === 'premium' ? 'default' : 'outline'} className="text-xs">
                                                            {search.user?.plan || 'free'}
                                                        </Badge>
                                                    </div>
                                                </div>
                                            ))
                                        ) : (
                                            <div className="text-center py-8">
                                                <Search className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                                <p className="text-sm text-muted-foreground">No recent searches</p>
                                            </div>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Recent Users */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Users className="h-5 w-5 text-blue-600" />
                                        Recent Users
                                    </CardTitle>
                                    <CardDescription>
                                        Latest user registrations
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {recentActivity.users.length > 0 ? (
                                            recentActivity.users.map((user) => (
                                                <div key={user.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-accent/50 transition-colors">
                                                    <div className="space-y-1">
                                                        <p className="text-sm font-medium leading-none text-foreground">
                                                            {user.name}
                                                        </p>
                                                        <p className="text-xs text-muted-foreground">
                                                            {user.email} • <span className="text-blue-600 font-medium">{user.searches_count}</span> searches
                                                        </p>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <Badge variant={user.plan === 'premium' ? 'default' : 'outline'} className="text-xs">
                                                            {user.plan}
                                                        </Badge>
                                                        <span className="text-xs text-muted-foreground">
                                                            {new Date(user.created_at).toLocaleDateString()}
                                                        </span>
                                                    </div>
                                                </div>
                                            ))
                                        ) : (
                                            <div className="text-center py-8">
                                                <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                                <p className="text-sm text-muted-foreground">No recent users</p>
                                            </div>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Top Performers */}
                        <div className="grid gap-6 md:grid-cols-2">
                            {/* Top Categories */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Package className="h-5 w-5 text-green-600" />
                                        Top Categories
                                    </CardTitle>
                                    <CardDescription>
                                        Categories with the most parts
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {topPerformers.categories.length > 0 ? (
                                            topPerformers.categories.map((category, index) => (
                                                <div key={category.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-accent/50 transition-colors">
                                                    <div className="space-y-1">
                                                        <p className="text-sm font-medium leading-none text-foreground">
                                                            {category.name}
                                                        </p>
                                                        <p className="text-xs text-muted-foreground">
                                                            <span className="text-green-600 font-medium">{category.parts_count}</span> parts available
                                                        </p>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <Badge variant={index < 3 ? "default" : "outline"} className="text-xs">
                                                            #{index + 1}
                                                        </Badge>
                                                    </div>
                                                </div>
                                            ))
                                        ) : (
                                            <div className="text-center py-8">
                                                <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                                <p className="text-sm text-muted-foreground">No categories with parts</p>
                                            </div>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Top Brands */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Eye className="h-5 w-5 text-blue-600" />
                                        Top Brands
                                    </CardTitle>
                                    <CardDescription>
                                        Brands with the most mobile models
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {topPerformers.brands.length > 0 ? (
                                            topPerformers.brands.map((brand, index) => (
                                                <div key={brand.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-accent/50 transition-colors">
                                                    <div className="space-y-1">
                                                        <p className="text-sm font-medium leading-none text-foreground">
                                                            {brand.name}
                                                        </p>
                                                        <p className="text-xs text-muted-foreground">
                                                            <span className="text-blue-600 font-medium">{brand.models_count}</span> models
                                                        </p>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <Badge variant={index < 3 ? "default" : "outline"} className="text-xs">
                                                            #{index + 1}
                                                        </Badge>
                                                    </div>
                                                </div>
                                            ))
                                        ) : (
                                            <div className="text-center py-8">
                                                <Eye className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                                <p className="text-sm text-muted-foreground">No brands with models</p>
                                            </div>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>
                </Tabs>

                {/* Quick Actions */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Calendar className="h-5 w-5 text-orange-600" />
                            Quick Actions
                        </CardTitle>
                        <CardDescription>
                            Common administrative tasks
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                            <a
                                href="/admin/categories"
                                className="group flex items-center justify-center p-6 border rounded-lg hover:bg-accent/50 hover:border-green-200 transition-all duration-200 hover:shadow-md"
                            >
                                <div className="text-center">
                                    <Package className="h-8 w-8 mx-auto mb-3 text-green-600 group-hover:scale-110 transition-transform" />
                                    <p className="text-sm font-medium text-foreground group-hover:text-green-700">Manage Categories</p>
                                    <p className="text-xs text-muted-foreground mt-1">Organize parts</p>
                                </div>
                            </a>
                            <a
                                href="/admin/brands"
                                className="group flex items-center justify-center p-6 border rounded-lg hover:bg-accent/50 hover:border-blue-200 transition-all duration-200 hover:shadow-md"
                            >
                                <div className="text-center">
                                    <ShoppingCart className="h-8 w-8 mx-auto mb-3 text-blue-600 group-hover:scale-110 transition-transform" />
                                    <p className="text-sm font-medium text-foreground group-hover:text-blue-700">Manage Brands</p>
                                    <p className="text-xs text-muted-foreground mt-1">Brand database</p>
                                </div>
                            </a>
                            <a
                                href="/admin/models"
                                className="group flex items-center justify-center p-6 border rounded-lg hover:bg-accent/50 hover:border-purple-200 transition-all duration-200 hover:shadow-md"
                            >
                                <div className="text-center">
                                    <Eye className="h-8 w-8 mx-auto mb-3 text-purple-600 group-hover:scale-110 transition-transform" />
                                    <p className="text-sm font-medium text-foreground group-hover:text-purple-700">Manage Models</p>
                                    <p className="text-xs text-muted-foreground mt-1">Device models</p>
                                </div>
                            </a>
                            <a
                                href="/admin/parts"
                                className="group flex items-center justify-center p-6 border rounded-lg hover:bg-accent/50 hover:border-orange-200 transition-all duration-200 hover:shadow-md"
                            >
                                <div className="text-center">
                                    <Activity className="h-8 w-8 mx-auto mb-3 text-orange-600 group-hover:scale-110 transition-transform" />
                                    <p className="text-sm font-medium text-foreground group-hover:text-orange-700">Manage Parts</p>
                                    <p className="text-xs text-muted-foreground mt-1">Parts inventory</p>
                                </div>
                            </a>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
