import { Head, router } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
    Search,
    Smartphone,
    Package,
    Tag,
    TrendingUp
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState, useEffect } from 'react';

interface Category {
    id: number;
    name: string;
}

interface Brand {
    id: number;
    name: string;
}

interface Filters {
    categories: Category[];
    brands: Brand[];
    manufacturers: string[];
    release_years: number[];
}

interface Suggestion {
    value: string;
    type: string;
    label?: string;
}

interface Props {
    filters: Filters;
}

export default function SearchIndex({ filters }: Props) {
    const [searchQuery, setSearchQuery] = useState('');
    const [searchType, setSearchType] = useState('all');
    const [selectedFilters, setSelectedFilters] = useState({
        category_id: 'all',
        brand_id: 'all',
        manufacturer: 'all',
        release_year: 'all',
        verified_only: false,
    });
    const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
    const [showSuggestions, setShowSuggestions] = useState(false);

    // Fetch suggestions when query changes
    useEffect(() => {
        if (searchQuery.length >= 2) {
            fetch(route('search.suggestions', { q: searchQuery }))
                .then(res => res.json())
                .then(data => {
                    setSuggestions(data);
                    setShowSuggestions(true);
                })
                .catch(() => setSuggestions([]));
        } else {
            setSuggestions([]);
            setShowSuggestions(false);
        }
    }, [searchQuery]);

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();

        const params = new URLSearchParams({
            q: searchQuery,
            type: searchType,
            ...Object.fromEntries(
                Object.entries(selectedFilters).filter(([, value]) => value !== 'all' && value !== '' && value !== false)
            )
        });

        router.get(route('search.results') + '?' + params.toString());
    };

    const handleSuggestionClick = (suggestion: Suggestion) => {
        setSearchQuery(suggestion.value);
        setShowSuggestions(false);
        
        // Auto-set search type based on suggestion
        if (suggestion.type !== 'part') {
            setSearchType(suggestion.type);
        }
    };

    const popularSearches = [
        { label: 'iPhone Display', type: 'part' },
        { label: 'Samsung Battery', type: 'part' },
        { label: 'Camera Module', type: 'category' },
        { label: 'Charging IC', type: 'category' },
        { label: 'OnePlus', type: 'brand' },
        { label: 'Xiaomi', type: 'brand' },
    ];

    return (
        <AppLayout>
            <Head title="Search Parts" />

            <div className="py-12">
                <div className="max-w-6xl mx-auto sm:px-6 lg:px-8">
                    {/* Hero Section */}
                    <div className="text-center mb-12">
                        <h1 className="text-4xl font-bold text-gray-900 mb-4">
                            Find Mobile Parts
                        </h1>
                        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                            Search through our comprehensive database of mobile device parts and components
                        </p>
                    </div>

                    {/* Search Form */}
                    <Card className="mb-8">
                        <CardContent className="p-6">
                            <form onSubmit={handleSearch} className="space-y-6">
                                {/* Main Search */}
                                <div className="relative">
                                    <div className="flex gap-3">
                                        <div className="flex-1 relative">
                                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                                            <Input
                                                type="text"
                                                placeholder="Search for parts, models, or brands..."
                                                value={searchQuery}
                                                onChange={(e) => setSearchQuery(e.target.value)}
                                                className="pl-10 h-12 text-lg"
                                                onFocus={() => setShowSuggestions(suggestions.length > 0)}
                                                onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
                                            />
                                            
                                            {/* Suggestions Dropdown */}
                                            {showSuggestions && suggestions.length > 0 && (
                                                <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-md shadow-lg z-10 mt-1">
                                                    {suggestions.slice(0, 8).map((suggestion, index) => (
                                                        <button
                                                            key={index}
                                                            type="button"
                                                            className="w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center justify-between"
                                                            onClick={() => handleSuggestionClick(suggestion)}
                                                        >
                                                            <span>{suggestion.value}</span>
                                                            <Badge variant="outline" className="text-xs">
                                                                {suggestion.type}
                                                            </Badge>
                                                        </button>
                                                    ))}
                                                </div>
                                            )}
                                        </div>
                                        
                                        <Select value={searchType} onValueChange={setSearchType}>
                                            <SelectTrigger className="w-40 h-12">
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All</SelectItem>
                                                <SelectItem value="part_name">Parts</SelectItem>
                                                <SelectItem value="model">Models</SelectItem>
                                                <SelectItem value="category">Categories</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        
                                        <Button type="submit" size="lg" className="px-8">
                                            <Search className="w-5 h-5 mr-2" />
                                            Search
                                        </Button>
                                    </div>
                                </div>

                                {/* Filters */}
                                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                    <div>
                                        <Select 
                                            value={selectedFilters.category_id} 
                                            onValueChange={(value) => setSelectedFilters(prev => ({ ...prev, category_id: value }))}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Category" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Categories</SelectItem>
                                                {filters.categories.map((category) => (
                                                    <SelectItem key={category.id} value={category.id.toString()}>
                                                        {category.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div>
                                        <Select 
                                            value={selectedFilters.brand_id} 
                                            onValueChange={(value) => setSelectedFilters(prev => ({ ...prev, brand_id: value }))}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Brand" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Brands</SelectItem>
                                                {filters.brands.map((brand) => (
                                                    <SelectItem key={brand.id} value={brand.id.toString()}>
                                                        {brand.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div>
                                        <Select 
                                            value={selectedFilters.manufacturer} 
                                            onValueChange={(value) => setSelectedFilters(prev => ({ ...prev, manufacturer: value }))}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Manufacturer" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Manufacturers</SelectItem>
                                                {filters.manufacturers?.filter(manufacturer => manufacturer && manufacturer.trim() !== '').map((manufacturer) => (
                                                    <SelectItem key={manufacturer} value={manufacturer}>
                                                        {manufacturer}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div>
                                        <Select 
                                            value={selectedFilters.release_year} 
                                            onValueChange={(value) => setSelectedFilters(prev => ({ ...prev, release_year: value }))}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Year" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Years</SelectItem>
                                                {filters.release_years?.filter(year => year && year.toString().trim() !== '').map((year) => (
                                                    <SelectItem key={year} value={year.toString()}>
                                                        {year}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>
                            </form>
                        </CardContent>
                    </Card>

                    {/* Popular Searches */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <TrendingUp className="w-5 h-5" />
                                Popular Searches
                            </CardTitle>
                            <CardDescription>
                                Quick access to commonly searched items
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex flex-wrap gap-2">
                                {popularSearches.map((search, index) => (
                                    <Button
                                        key={index}
                                        variant="outline"
                                        size="sm"
                                        onClick={() => {
                                            setSearchQuery(search.label);
                                            setSearchType(search.type);
                                        }}
                                        className="text-sm"
                                    >
                                        {search.label}
                                    </Button>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Quick Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <Card>
                            <CardContent className="p-6 text-center">
                                <Package className="w-12 h-12 mx-auto mb-4 text-green-600" />
                                <h3 className="text-2xl font-bold text-gray-900 mb-2">10,000+</h3>
                                <p className="text-gray-600">Parts Available</p>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardContent className="p-6 text-center">
                                <Smartphone className="w-12 h-12 mx-auto mb-4 text-blue-600" />
                                <h3 className="text-2xl font-bold text-gray-900 mb-2">500+</h3>
                                <p className="text-gray-600">Mobile Models</p>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardContent className="p-6 text-center">
                                <Tag className="w-12 h-12 mx-auto mb-4 text-purple-600" />
                                <h3 className="text-2xl font-bold text-gray-900 mb-2">50+</h3>
                                <p className="text-gray-600">Categories</p>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
