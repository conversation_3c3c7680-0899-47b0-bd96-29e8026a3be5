import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
    Search,
    Package,
    Eye,
    Heart,
    ChevronLeft,
    ChevronRight,
    Grid,
    List,
    SlidersHorizontal,
    ArrowLeft,
    Building2
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState, useEffect, useRef } from 'react';

interface Brand {
    id: number;
    name: string;
    description?: string;
    slug?: string;
}

interface Category {
    id: number;
    name: string;
}

interface Part {
    id: number;
    name: string;
    slug?: string;
    part_number: string | null;
    manufacturer: string | null;
    description: string | null;
    images: string[] | null;
    category: {
        id: number;
        name: string;
    };
    models: Array<{
        id: number;
        name: string;
        brand: {
            id: number;
            name: string;
        };
    }>;
}

interface Results {
    data: Part[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

interface Filters {
    categories: Category[];
    brands: Brand[];
    manufacturers: string[];
    release_years: number[];
}

interface AppliedFilters {
    category_id?: string;
    manufacturer?: string;
    release_year?: string;
    [key: string]: string | boolean | null | undefined;
}

interface Suggestion {
    value: string;
    type: string;
    label?: string;
}

interface Props {
    brand: Brand;
    filters: Filters;
    results?: Results;
    applied_filters?: AppliedFilters;
    search_type?: string;
    query?: string;
    remaining_searches?: number;
}

export default function BrandSearch({ 
    brand, 
    filters, 
    results, 
    applied_filters = {}, 
    search_type = 'all', 
    query = '', 
    remaining_searches 
}: Props) {
    const [searchQuery, setSearchQuery] = useState(query);
    // In brand search, default to 'all' if search_type is not appropriate
    const [searchType, setSearchType] = useState(
        ['all', 'part_name', 'model'].includes(search_type) ? search_type : 'all'
    );


    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
    const [showFilters, setShowFilters] = useState(false);
    const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
    const [showSuggestions, setShowSuggestions] = useState(false);
    const [isSearching, setIsSearching] = useState(false);
    const searchContainerRef = useRef<HTMLDivElement>(null);

    // Sync local state with props when they change
    useEffect(() => {

        setSearchQuery(query);
        // Only update search type if it's appropriate for brand search
        const appropriateSearchType = ['all', 'part_name', 'model'].includes(search_type) ? search_type : 'all';
        setSearchType(appropriateSearchType);
        // Reset searching state when new props arrive (after navigation)
        setIsSearching(false);
        setShowSuggestions(false);
    }, [query, search_type, results]);

    // Reset isSearching when results arrive and ensure state consistency
    // Use a separate variable to track results changes
    const resultsKey = results ? `${results.current_page}-${results.total}-${results.data.length}` : 'no-results';
    useEffect(() => {
        if (results !== undefined) {
            setIsSearching(false);
            setShowSuggestions(false);
        }
    }, [results, resultsKey]);

    // Fetch suggestions when search query changes
    useEffect(() => {
        if (searchQuery.length >= 2 && !isSearching) {
            const params = new URLSearchParams({
                q: searchQuery,
                brand_id: brand.id.toString()
            });

            fetch(route('search.suggestions') + '?' + params.toString())
                .then(response => response.json())
                .then(data => {
                    setSuggestions(data);
                    // Only show suggestions if:
                    // 1. We have suggestions
                    // 2. We're not currently performing a search
                    // 3. The search query doesn't match current results (to avoid showing suggestions over results)
                    const isCurrentSearch = results && searchQuery === query;
                    if (data.length > 0 && !isSearching && !isCurrentSearch) {
                        setShowSuggestions(true);
                    }
                })
                .catch(() => {
                    setSuggestions([]);
                    setShowSuggestions(false);
                });
        } else {
            setSuggestions([]);
            setShowSuggestions(false);
        }
    }, [searchQuery, results, query, brand.id, isSearching]);

    // Handle click outside to close suggestions
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (searchContainerRef.current && !searchContainerRef.current.contains(event.target as Node)) {
                setShowSuggestions(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Timeout fallback to reset isSearching state if it gets stuck
    useEffect(() => {
        if (isSearching) {
            const timeout = setTimeout(() => {
                console.warn('Search timeout - resetting isSearching state after 8 seconds');
                setIsSearching(false);
                setShowSuggestions(false);
            }, 8000); // 8 second timeout (reduced from 10)

            return () => clearTimeout(timeout);
        }
    }, [isSearching]);

    // Additional safety mechanism - reset isSearching on component mount/unmount
    useEffect(() => {
        return () => {
            setIsSearching(false);
            setShowSuggestions(false);
        };
    }, []);

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();

        // Validate search query
        if (!searchQuery.trim()) {
            console.warn('Empty search query');
            return;
        }

        console.log('Starting brand search:', { searchQuery, searchType, isSearching });

        // Set searching state to prevent suggestions from showing
        setIsSearching(true);
        setShowSuggestions(false);

        const params = new URLSearchParams({
            q: searchQuery,
            type: searchType,
            ...Object.fromEntries(
                Object.entries(applied_filters).filter(([, value]) =>
                    value !== 'all' &&
                    value !== '' &&
                    value !== false &&
                    value !== null &&
                    value !== 'null' &&
                    value !== undefined
                )
            )
        });

        const searchUrl = route('search.brand', brand.slug || brand.id) + '?' + params.toString();

        // Add error handling for navigation
        try {
            router.get(searchUrl, {}, {
                onError: (errors) => {
                    console.error('Search navigation error:', errors);
                    setIsSearching(false);
                },
                onFinish: () => {
                    // This will run regardless of success/failure
                    // The isSearching state will be reset by the useEffect when results arrive
                }
            });
        } catch (error) {
            console.error('Search error:', error);
            setIsSearching(false);
        }
    };

    const handleSuggestionClick = (suggestion: { type: string; value: string; brand?: string }) => {
        // Set searching state immediately to prevent interference
        setIsSearching(true);
        setShowSuggestions(false);

        // Update local state
        setSearchQuery(suggestion.value);

        // In brand search, we should use 'all' search type to search across all fields
        // since we're already filtering by brand. Don't change search type based on suggestions.
        // Keep the current search type - don't change it based on suggestion type

        // Small delay to ensure state is updated before navigation
        setTimeout(() => {
            const params = new URLSearchParams({
                q: suggestion.value,
                type: searchType,
                ...Object.fromEntries(
                    Object.entries(applied_filters).filter(([, value]) =>
                        value !== 'all' &&
                        value !== '' &&
                        value !== false &&
                        value !== null &&
                        value !== 'null' &&
                        value !== undefined
                    )
                )
            });

            const searchUrl = route('search.brand', brand.slug || brand.id) + '?' + params.toString();

            try {
                router.get(searchUrl, {}, {
                    onError: (errors) => {
                        console.error('Suggestion search navigation error:', errors);
                        setIsSearching(false);
                    },
                    onFinish: () => {
                        // The isSearching state will be reset by the useEffect when results arrive
                    }
                });
            } catch (error) {
                console.error('Suggestion search error:', error);
                setIsSearching(false);
            }
        }, 50);
    };

    const handlePageChange = (page: number) => {
        const currentParams = new URLSearchParams(window.location.search);
        const params = new URLSearchParams();

        // Only preserve valid parameters
        for (const [key, value] of currentParams.entries()) {
            if (value !== 'all' &&
                value !== '' &&
                value !== 'false' &&
                value !== 'null' &&
                value !== null &&
                value !== undefined) {
                params.set(key, value);
            }
        }

        params.set('page', page.toString());
        router.get(window.location.pathname + '?' + params.toString());
    };

    const handleFilterChange = (key: string, value: string) => {
        const currentParams = new URLSearchParams(window.location.search);
        const params = new URLSearchParams();

        // Only preserve valid parameters
        for (const [paramKey, paramValue] of currentParams.entries()) {
            if (paramValue !== 'all' &&
                paramValue !== '' &&
                paramValue !== 'false' &&
                paramValue !== 'null' &&
                paramValue !== null &&
                paramValue !== undefined) {
                params.set(paramKey, paramValue);
            }
        }

        if (value && value !== 'all') {
            params.set(key, value);
        } else {
            params.delete(key);
        }
        params.delete('page'); // Reset to first page when filtering
        router.get(window.location.pathname + '?' + params.toString());
    };

    const PartCard = ({ part }: { part: Part }) => (
        <Card className="hover:shadow-lg transition-shadow">
            <CardContent className="p-4">
                <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                        <h3 className="font-semibold text-lg text-gray-900 mb-1">
                            {part.name}
                        </h3>
                        {part.part_number && (
                            <p className="text-sm text-gray-600 mb-1">
                                Part #: {part.part_number}
                            </p>
                        )}
                        {part.manufacturer && (
                            <p className="text-sm text-gray-600 mb-2">
                                by {part.manufacturer}
                            </p>
                        )}
                    </div>
                    <Button variant="ghost" size="sm">
                        <Heart className="w-4 h-4" />
                    </Button>
                </div>

                <div className="mb-3">
                    <Badge variant="outline" className="mb-2">
                        {part.category.name}
                    </Badge>
                    {part.description && (
                        <p className="text-sm text-gray-600 line-clamp-2">
                            {part.description}
                        </p>
                    )}
                </div>

                {part.models.length > 0 && (
                    <div className="mb-4">
                        <p className="text-xs text-gray-500 mb-1">Compatible with:</p>
                        <div className="flex flex-wrap gap-1">
                            {part.models.slice(0, 3).map((model) => (
                                <Badge key={model.id} variant="secondary" className="text-xs">
                                    {model.brand.name} {model.name}
                                </Badge>
                            ))}
                            {part.models.length > 3 && (
                                <Badge variant="secondary" className="text-xs">
                                    +{part.models.length - 3} more
                                </Badge>
                            )}
                        </div>
                    </div>
                )}

                <div className="flex justify-between items-center">
                    <Link href={route('parts.show', part.slug || part.id)}>
                        <Button size="sm">
                            <Eye className="w-4 h-4 mr-2" />
                            View Details
                        </Button>
                    </Link>
                </div>
            </CardContent>
        </Card>
    );

    const PartListItem = ({ part }: { part: Part }) => (
        <Card>
            <CardContent className="p-4">
                <div className="flex items-center justify-between">
                    <div className="flex-1">
                        <div className="flex items-start gap-4">
                            <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                                <Package className="w-8 h-8 text-gray-400" />
                            </div>
                            <div className="flex-1">
                                <h3 className="font-semibold text-lg text-gray-900 mb-1">
                                    {part.name}
                                </h3>
                                <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                                    {part.part_number && <span>Part #: {part.part_number}</span>}
                                    {part.manufacturer && <span>by {part.manufacturer}</span>}
                                    <Badge variant="outline">{part.category.name}</Badge>
                                </div>
                                {part.description && (
                                    <p className="text-sm text-gray-600 mb-2 line-clamp-1">
                                        {part.description}
                                    </p>
                                )}
                                {part.models.length > 0 && (
                                    <div className="flex flex-wrap gap-1">
                                        {part.models.slice(0, 5).map((model) => (
                                            <Badge key={model.id} variant="secondary" className="text-xs">
                                                {model.brand.name} {model.name}
                                            </Badge>
                                        ))}
                                        {part.models.length > 5 && (
                                            <Badge variant="secondary" className="text-xs">
                                                +{part.models.length - 5} more
                                            </Badge>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button variant="ghost" size="sm">
                            <Heart className="w-4 h-4" />
                        </Button>
                        <Link href={route('parts.show', part.slug || part.id)}>
                            <Button size="sm">
                                <Eye className="w-4 h-4 mr-2" />
                                View Details
                            </Button>
                        </Link>
                    </div>
                </div>
            </CardContent>
        </Card>
    );

    return (
        <AppLayout key={`brand-search-${brand.id}`}>
            <Head title={`Search ${brand.name} Parts`} />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-6">
                        <div className="flex items-center gap-2 mb-4">
                            <Link href={route('brands.show', brand.slug || brand.id)}>
                                <Button variant="ghost" size="sm">
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back to {brand.name}
                                </Button>
                            </Link>
                        </div>
                        <div className="flex items-center gap-3 mb-2">
                            <Building2 className="w-8 h-8 text-blue-600" />
                            <h1 className="text-3xl font-bold text-gray-900">
                                Search {brand.name} Parts
                            </h1>
                        </div>
                        <p className="text-gray-600">
                            Find parts specifically for {brand.name} devices
                            {remaining_searches !== undefined && remaining_searches !== -1 && (
                                <span className="ml-2">
                                    • {remaining_searches} searches remaining today
                                </span>
                            )}
                        </p>
                    </div>

                    {/* Search Form */}
                    <Card className="mb-6">
                        <CardContent className="p-6">
                            <form onSubmit={handleSearch} className="space-y-4">
                                <div className="relative" ref={searchContainerRef}>
                                    <div className="flex gap-3">
                                        <div className="flex-1 relative">
                                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                                            <Input
                                                type="text"
                                                placeholder={`Search for ${brand.name} parts...`}
                                                value={searchQuery}
                                                onChange={(e) => {
                                                    const newValue = e.target.value;
                                                    setSearchQuery(newValue);

                                                    // Reset searching state when user types
                                                    setIsSearching(false);

                                                    // Clear suggestions if input is empty
                                                    if (!newValue.trim()) {
                                                        setSuggestions([]);
                                                        setShowSuggestions(false);
                                                    }
                                                }}
                                                className="pl-10 h-12 text-lg"
                                                onFocus={() => {
                                                    // Only show suggestions if:
                                                    // 1. We have suggestions
                                                    // 2. We're not currently searching
                                                    // 3. The current input doesn't match the displayed results
                                                    const isCurrentSearch = results && searchQuery === query;
                                                    if (suggestions.length > 0 && !isSearching && !isCurrentSearch && searchQuery.length >= 2) {
                                                        setShowSuggestions(true);
                                                    }
                                                }}
                                                onBlur={() => {
                                                    // Delay hiding suggestions to allow for clicks
                                                    setTimeout(() => setShowSuggestions(false), 200);
                                                }}
                                            />
                                            
                                            {/* Suggestions Dropdown */}
                                            {showSuggestions && suggestions.length > 0 && (
                                                <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-md shadow-lg z-10 mt-1">
                                                    {suggestions.slice(0, 8).map((suggestion, index) => (
                                                        <button
                                                            key={index}
                                                            type="button"
                                                            className="w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center justify-between"
                                                            onClick={() => handleSuggestionClick(suggestion)}
                                                        >
                                                            <span>{suggestion.value}</span>
                                                            <Badge variant="outline" className="text-xs">
                                                                {suggestion.type}
                                                            </Badge>
                                                        </button>
                                                    ))}
                                                </div>
                                            )}
                                        </div>
                                        
                                        <Select value={searchType} onValueChange={setSearchType}>
                                            <SelectTrigger className="w-40 h-12">
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All</SelectItem>
                                                <SelectItem value="part_name">Parts</SelectItem>
                                                <SelectItem value="model">Models</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        
                                        <Button type="submit" size="lg" className="px-8">
                                            <Search className="w-5 h-5 mr-2" />
                                            Search
                                        </Button>
                                    </div>
                                </div>
                            </form>
                        </CardContent>
                    </Card>

                    {/* Results Section */}
                    {isSearching ? (
                        <Card>
                            <CardContent className="text-center py-12">
                                <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                    Searching...
                                </h3>
                                <p className="text-gray-600">
                                    Finding {brand.name} parts for you
                                </p>
                            </CardContent>
                        </Card>
                    ) : results ? (
                        <>
                            {/* Results Header */}
                            <div className="flex items-center justify-between mb-6">
                                <div>
                                    <h2 className="text-xl font-semibold text-gray-900">
                                        Search Results
                                    </h2>
                                    <p className="text-gray-600">
                                        {results.total} {brand.name} parts found for "{query}"
                                    </p>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Button
                                        variant={viewMode === 'grid' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => setViewMode('grid')}
                                    >
                                        <Grid className="w-4 h-4" />
                                    </Button>
                                    <Button
                                        variant={viewMode === 'list' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => setViewMode('list')}
                                    >
                                        <List className="w-4 h-4" />
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => setShowFilters(!showFilters)}
                                    >
                                        <SlidersHorizontal className="w-4 h-4 mr-2" />
                                        Filters
                                    </Button>
                                </div>
                            </div>

                            {/* Filters */}
                            {showFilters && (
                                <Card className="mb-6">
                                    <CardHeader>
                                        <CardTitle>Additional Filters</CardTitle>
                                        <CardDescription>
                                            Narrow down your search within {brand.name} parts
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <Select
                                                value={applied_filters.category_id || 'all'}
                                                onValueChange={(value) => handleFilterChange('category_id', value)}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Category" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="all">All Categories</SelectItem>
                                                    {filters.categories.map((category: Category) => (
                                                        <SelectItem key={category.id} value={category.id.toString()}>
                                                            {category.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>

                                            <Select
                                                value={applied_filters.manufacturer || 'all'}
                                                onValueChange={(value) => handleFilterChange('manufacturer', value)}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Manufacturer" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="all">All Manufacturers</SelectItem>
                                                    {filters.manufacturers?.filter(manufacturer => manufacturer && manufacturer.trim() !== '').map((manufacturer: string) => (
                                                        <SelectItem key={manufacturer} value={manufacturer}>
                                                            {manufacturer}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>

                                            <Select
                                                value={applied_filters.release_year || 'all'}
                                                onValueChange={(value) => handleFilterChange('release_year', value)}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Year" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="all">All Years</SelectItem>
                                                    {filters.release_years?.filter(year => year && year.toString().trim() !== '').map((year: number) => (
                                                        <SelectItem key={year} value={year.toString()}>
                                                            {year}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}

                            {/* Results */}
                            {results.data.length > 0 ? (
                                <>
                                    <div className={
                                        viewMode === 'grid'
                                            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8'
                                            : 'space-y-4 mb-8'
                                    }>
                                        {results.data.map((part) => (
                                            viewMode === 'grid'
                                                ? <PartCard key={part.id} part={part} />
                                                : <PartListItem key={part.id} part={part} />
                                        ))}
                                    </div>

                                    {/* Pagination */}
                                    {results.last_page > 1 && (
                                        <div className="flex items-center justify-between">
                                            <p className="text-sm text-gray-600">
                                                Showing {results.from} to {results.to} of {results.total} results
                                            </p>
                                            <div className="flex items-center gap-2">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => handlePageChange(results.current_page - 1)}
                                                    disabled={results.current_page === 1}
                                                >
                                                    <ChevronLeft className="w-4 h-4" />
                                                    Previous
                                                </Button>

                                                {Array.from({ length: Math.min(5, results.last_page) }, (_, i) => {
                                                    const page = i + 1;
                                                    return (
                                                        <Button
                                                            key={page}
                                                            variant={page === results.current_page ? 'default' : 'outline'}
                                                            size="sm"
                                                            onClick={() => handlePageChange(page)}
                                                        >
                                                            {page}
                                                        </Button>
                                                    );
                                                })}

                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => handlePageChange(results.current_page + 1)}
                                                    disabled={results.current_page === results.last_page}
                                                >
                                                    Next
                                                    <ChevronRight className="w-4 h-4" />
                                                </Button>
                                            </div>
                                        </div>
                                    )}
                                </>
                            ) : (
                                <Card>
                                    <CardContent className="text-center py-12">
                                        <Search className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                            No {brand.name} parts found
                                        </h3>
                                        <p className="text-gray-600 mb-6">
                                            Try adjusting your search terms or filters
                                        </p>
                                        <Button onClick={() => {
                                            // Clear local state
                                            setSearchQuery('');
                                            setSearchType('all');
                                            setIsSearching(false);
                                            setShowSuggestions(false);
                                            setSuggestions([]);

                                            // Navigate to clear server-side state
                                            router.get(route('search.brand', brand.slug || brand.id));
                                        }}>
                                            <Search className="w-4 h-4 mr-2" />
                                            Clear Search
                                        </Button>
                                    </CardContent>
                                </Card>
                            )}
                        </>
                    ) : (
                        /* No search performed yet - show brand info */
                        <Card>
                            <CardContent className="text-center py-12">
                                <Building2 className="w-16 h-16 mx-auto mb-4 text-blue-600" />
                                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                    Search {brand.name} Parts
                                </h3>
                                <p className="text-gray-600 mb-6">
                                    {brand.description || `Find parts specifically for ${brand.name} devices`}
                                </p>
                                <p className="text-sm text-gray-500">
                                    Enter a search term above to find {brand.name} parts
                                </p>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
