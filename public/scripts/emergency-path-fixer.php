<?php

// EMERGENCY PATH FIXER FOR LARAVEL PRODUCTION
// This script specifically addresses the path mismatch issue you're experiencing
// Access via: https://yourdomain.com/scripts/emergency-path-fixer.php

// Define the base path
$basePath = __DIR__ . '/../../';
$currentPath = realpath($basePath);

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Emergency Path Fixer</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🚨 Emergency Path Fixer</h1>
        <p><strong>Current Path:</strong> $currentPath</p>
        <p><strong>Target:</strong> Fix path mismatch causing view compilation errors</p>
        <hr>";

$results = [];
$totalSteps = 6;
$currentStep = 1;

// Step 1: Clear Bootstrap Cache
echo "<div class='step'>
    <h3>Step $currentStep/$totalSteps: Clear Bootstrap Cache</h3>";

$bootstrapCachePath = $basePath . 'bootstrap/cache/';
$cleared = 0;
$errors = 0;

if (is_dir($bootstrapCachePath)) {
    $files = glob($bootstrapCachePath . '*.php');
    foreach ($files as $file) {
        if (basename($file) !== '.gitignore') {
            if (unlink($file)) {
                $cleared++;
                echo "<div class='success'>✓ Deleted: " . basename($file) . "</div>";
            } else {
                $errors++;
                echo "<div class='error'>✗ Failed to delete: " . basename($file) . "</div>";
            }
        }
    }
    
    if ($cleared > 0) {
        echo "<div class='success'>✓ Cleared $cleared bootstrap cache files</div>";
    }
    if ($errors > 0) {
        echo "<div class='error'>✗ Failed to clear $errors files</div>";
    }
    if ($cleared === 0 && $errors === 0) {
        echo "<div class='info'>ℹ No bootstrap cache files to clear</div>";
    }
} else {
    echo "<div class='warning'>⚠ Bootstrap cache directory not found</div>";
}

echo "</div>";
$currentStep++;

// Step 2: Clear View Cache
echo "<div class='step'>
    <h3>Step $currentStep/$totalSteps: Clear View Cache</h3>";

$viewCachePath = $basePath . 'storage/framework/views/';
$viewCleared = 0;
$viewErrors = 0;

if (is_dir($viewCachePath)) {
    $viewFiles = glob($viewCachePath . '*.php');
    foreach ($viewFiles as $file) {
        if (unlink($file)) {
            $viewCleared++;
            echo "<div class='success'>✓ Deleted view cache: " . basename($file) . "</div>";
        } else {
            $viewErrors++;
            echo "<div class='error'>✗ Failed to delete view cache: " . basename($file) . "</div>";
        }
    }
    
    if ($viewCleared > 0) {
        echo "<div class='success'>✓ Cleared $viewCleared view cache files</div>";
    }
    if ($viewErrors > 0) {
        echo "<div class='error'>✗ Failed to clear $viewErrors view files</div>";
    }
    if ($viewCleared === 0 && $viewErrors === 0) {
        echo "<div class='info'>ℹ No view cache files to clear</div>";
    }
} else {
    echo "<div class='warning'>⚠ View cache directory not found</div>";
}

echo "</div>";
$currentStep++;

// Step 3: Ensure Critical Directories
echo "<div class='step'>
    <h3>Step $currentStep/$totalSteps: Ensure Critical Directories</h3>";

$criticalDirs = [
    'storage/framework/views',
    'storage/framework/cache',
    'storage/framework/sessions',
    'storage/logs',
    'bootstrap/cache'
];

foreach ($criticalDirs as $dir) {
    $fullPath = $basePath . $dir;
    if (!is_dir($fullPath)) {
        if (mkdir($fullPath, 0775, true)) {
            echo "<div class='success'>✓ Created directory: $dir</div>";
        } else {
            echo "<div class='error'>✗ Failed to create directory: $dir</div>";
        }
    } else {
        // Check if writable
        if (is_writable($fullPath)) {
            echo "<div class='success'>✓ Directory exists and is writable: $dir</div>";
        } else {
            if (chmod($fullPath, 0775)) {
                echo "<div class='success'>✓ Fixed permissions for: $dir</div>";
            } else {
                echo "<div class='error'>✗ Directory exists but not writable: $dir</div>";
            }
        }
    }
}

echo "</div>";
$currentStep++;

// Step 4: Remove Hot File
echo "<div class='step'>
    <h3>Step $currentStep/$totalSteps: Remove Hot File</h3>";

$hotFile = $basePath . 'public/hot';
if (file_exists($hotFile)) {
    if (unlink($hotFile)) {
        echo "<div class='success'>✓ Removed hot file (development artifact)</div>";
    } else {
        echo "<div class='error'>✗ Failed to remove hot file</div>";
    }
} else {
    echo "<div class='success'>✓ No hot file found (good for production)</div>";
}

echo "</div>";
$currentStep++;

// Step 5: Check Environment
echo "<div class='step'>
    <h3>Step $currentStep/$totalSteps: Environment Check</h3>";

$envFile = $basePath . '.env';
if (file_exists($envFile) && is_readable($envFile)) {
    echo "<div class='success'>✓ .env file exists and is readable</div>";
    
    // Check for development settings
    $envContent = file_get_contents($envFile);
    if (strpos($envContent, 'APP_ENV=local') !== false) {
        echo "<div class='warning'>⚠ APP_ENV is set to 'local' - should be 'production'</div>";
    }
    if (strpos($envContent, 'APP_DEBUG=true') !== false) {
        echo "<div class='warning'>⚠ APP_DEBUG is set to 'true' - should be 'false' for production</div>";
    }
} else {
    echo "<div class='error'>✗ .env file missing or not readable</div>";
}

echo "</div>";
$currentStep++;

// Step 6: Final Status
echo "<div class='step'>
    <h3>Step $currentStep/$totalSteps: Final Status & Next Steps</h3>";

echo "<div class='success'>✓ Emergency path fixing completed!</div>";

echo "<h4>What was fixed:</h4>
<ul>
    <li>Cleared all cached configuration files that might contain wrong paths</li>
    <li>Cleared all compiled view files that contained development paths</li>
    <li>Ensured all critical directories exist with proper permissions</li>
    <li>Removed development artifacts (hot file)</li>
</ul>

<h4>Next Steps:</h4>
<ol>
    <li><strong>Test your site now</strong> - The path error should be resolved</li>
    <li><strong>Delete this script</strong> - Remove this file for security</li>
    <li>If issues persist, check your .env file for correct paths</li>
    <li>Consider running the full Laravel error fixer script</li>
</ol>";

echo "</div>";

echo "
        <div style='margin-top: 30px; padding: 20px; background: #e9ecef; border-radius: 8px; text-align: center;'>
            <h3>🔒 Security Notice</h3>
            <p><strong>DELETE THIS SCRIPT IMMEDIATELY AFTER USE!</strong></p>
            <p>File location: " . __FILE__ . "</p>
        </div>
        
        <div style='margin-top: 20px; text-align: center; color: #6c757d;'>
            <p>Emergency Path Fixer executed at: " . date('Y-m-d H:i:s T') . "</p>
        </div>
    </div>
</body>
</html>";

?>
