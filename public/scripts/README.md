# Laravel Maintenance Scripts for cPanel Shared Hosting

## 1. `laravel_prod_error-fixer.php` - Production Error Fixer

**What it does:**
🛠️ Fixes common Laravel production errors:
- File permission issues
- Missing storage directories
- Cache clearance (config, route, view)
- Environment verification
- Path configuration checks

**How to run on cPanel:**
1. Upload to: `public_html/public/scripts/laravel_prod_error-fixer.php`
2. Run via browser:
   `https://yourdomain.com/scripts/laravel_prod_error-fixer.php`
3. Review the automated fixes applied
4. Delete immediately after use

## 2. `emergency-path-fixer.php` - Emergency Path Fixer

**What it does:**
🛤️ **EMERGENCY TOOL** for critical path mismatch errors:
- Fixes view compilation errors caused by development paths in production
- Clears bootstrap cache containing wrong paths
- Clears view cache with hardcoded development paths
- Ensures critical directories exist with proper permissions
- Removes development artifacts (hot file)

**When to use:**
- Getting errors like: `file_put_contents(/Volumes/Storage/...): Failed to open stream`
- <PERSON><PERSON> trying to write to local development paths instead of production paths
- View compilation failures in production

**How to run on cPanel:**
1. Upload to: `public_html/public/scripts/emergency-path-fixer.php`
2. Run via browser:
   `https://yourdomain.com/scripts/emergency-path-fixer.php`
3. **Use immediately when experiencing path errors**
4. Delete immediately after use

## 3. `laravel_symlink_creator.php` - Storage Symlink Creator

**What it does:**
🔗 Creates `public/storage` → `storage/app/public` symlink  
🔄 Falls back to directory copy if symlinks disabled  
📁 Creates placeholder images if missing  
✅ Verifies symlink functionality  
📱 Mobile-friendly interface  

**How to run on cPanel:**
1. Upload to: `public_html/public/scripts/laravel_symlink_creator.php`
2. Run via browser:  
   `https://yourdomain.com/scripts/laravel_symlink_creator.php`
3. Check output for success/failure
4. Delete immediately after use

## 4. `laravel_permissions_fixer.php` - File Permission Fixer

**What it does:**

✅ Sets correct permissions (755 for directories, 644 for files)  
✅ Special permissions for storage (775) and bootstrap/cache (775)  
✅ Creates missing directories automatically  
✅ Skips vendor/node_modules/.git directories  
✅ Adds security .htaccess files to sensitive directories  

**How to run on cPanel:**
1. Upload to: `public_html/public/scripts/laravel_permissions_fixer.php`
2. Run via browser:  
   `https://yourdomain.com/scripts/laravel_permissions_fixer.php`
3. Delete immediately after use

## 5. `laravel_run_artisan.php` - Artisan Command Runner

**What it does:**
⚙️ Safely executes Artisan commands via web:
- `migrate`  
- `cache:clear`  
- `storage:link`  
- Custom command support  
🔒 Password protection option  

**How to run on cPanel:**
1. Upload to: `public_html/public/scripts/laravel_run_artisan.php`
2. Edit script to set password (line 15)
3. Run via browser:  
   `https://yourdomain.com/scripts/laravel_run_artisan.php`
4. Delete immediately after use

## 6. `populate_product_slugs.php` - Product Slug Generator

**What it does:**
🔤 Generates slugs for products that don't have one:
- Finds all products with missing slugs  
- Creates URL-friendly slugs from product names  
- Ensures slug uniqueness (adds numbers if needed)  
- Updates the database with generated slugs  
📊 Progress tracking and results table  

**How to run on cPanel:**
1. Upload to: `public_html/public/scripts/populate_product_slugs.php`
2. Run via browser:  
   `https://yourdomain.com/scripts/populate_product_slugs.php`
3. Click "Generate Missing Product Slugs" button
4. Delete immediately after use

## 7. `laravel_db_restore.php` - Database Restore Tool

**What it does:**
🗄️ Manages your database for backup/restore operations:
- Deletes all tables from your database  
- Restores database from an SQL dump file  
- Lists all current tables in the database  
- Handles complex SQL files with stored procedures  
- Provides detailed error reporting  
- Includes safety confirmation checks  

**How to run on cPanel:**
1. Upload to: `public_html/public/scripts/laravel_db_restore.php`
2. Run via browser:  
   `https://yourdomain.com/scripts/laravel_db_restore.php`
3. Choose an operation:
   - Drop all tables
   - Import SQL file
   - Drop all tables and import SQL file
4. Delete immediately after use

## 8. `laravel_developer_toolkit.php` - Developer Toolkit Dashboard

**What it does:**
🎛️ **Central dashboard** for all Laravel development tools:
- Provides a unified interface to access all maintenance scripts
- Includes statistics and tool descriptions
- Mobile-friendly responsive design
- Quick access to all available tools

**Features:**
- Database Migration Tool
- Database Restore Tool
- NPM Build Manager
- Permissions Fixer
- Production Error Fixer
- Emergency Path Fixer
- Artisan Command Runner
- Storage Symlink Creator

**How to run on cPanel:**
1. Upload to: `public_html/public/scripts/laravel_developer_toolkit.php`
2. Run via browser:
   `https://yourdomain.com/scripts/laravel_developer_toolkit.php`
3. Click on any tool to launch it
4. Delete after use or protect with authentication

## Security Best Practices
1. Always delete scripts after use
2. Never leave scripts in public directories
3. Use .htaccess to block script directory if possible:
   ```apache
   <Files *.php>
      Deny from all
   </Files>

## 5. companion .htaccess security template:

```apache
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Standard Laravel redirect to public/
    RewriteRule ^(.*)$ public/$1 [L]
    
    # Security Headers
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Block access to sensitive files
    <FilesMatch "(\.env|\.env.example|\.gitignore|composer\.json|composer\.lock|package\.json|webpack\.mix\.js)$">
        Require all denied
    </FilesMatch>
    
    # Block access to hidden files/folders
    RewriteCond %{SCRIPT_FILENAME} -d [OR]
    RewriteCond %{SCRIPT_FILENAME} -f
    RewriteRule "(^|/)\.(?!well-known)" - [F]
</IfModule>

# php -- BEGIN cPanel-generated handler, do not edit
# Set the "ea-php82" package as the default "PHP" programming language.
<IfModule mime_module>
    AddHandler application/x-httpd-ea-php82 .php .php8 .phtml
</IfModule>
# php -- END cPanel-generated handler, do not edit

# Additional security measures
<IfModule mod_headers.c>
    # Disable server signature
    ServerSignature Off
    
    # Prevent MIME sniffing
    Header set X-Content-Type-Options "nosniff"
    
    # Enable CSP (Content Security Policy) - adjust as needed
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https:; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: https:; font-src 'self' https:;"
</IfModule>

# Protect the scripts directory
<Directory "/public/scripts">
    <FilesMatch "\.(php|php5|phtml)$">
        Require all denied
    </FilesMatch>
</Directory>

# Disable directory browsing
Options -Indexes

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript
</IfModule>
```

## Key Security Features Added

### Enhanced Headers
- **XSS Protection**: Blocks cross-site scripting attacks  
- **MIME Sniffing Prevention**: Stops browsers from interpreting files as different MIME types  

### File Protection
- **Sensitive Files**: Blocks access to `.env`, config files, and development files  
- **Hidden Files**: Prevents access to all dotfiles (except `.well-known`)  
- **Script Directory**: Specifically blocks PHP execution in `/public/scripts`  

### Server Hardening
- **Directory Browsing**: Disables automatic directory index views  
- **Content Security Policy**: Basic CSP template (adjust as needed for your app)  
- **Compression**: Improves performance for text-based assets  

### Additional Recommendation
For maximum security, create a separate [.htaccess](cci:7://file:///Volumes/Storage/SaaS%20Business%20Projects/megaskyshop-laravel/.htaccess:0:0-0:0) in your `public/scripts` directory with:

```apache
<FilesMatch "\.(php|php5|phtml)$">
    Require all denied
</FilesMatch>
```

## Quick Reference

### Emergency Situations
- **Path errors in production**: Use `emergency-path-fixer.php` first
- **General production issues**: Use `laravel_prod_error-fixer.php`
- **Storage/symlink issues**: Use `laravel_symlink_creator.php`

### Development Tasks
- **Database migrations**: Use `laravel_db_migrate.php`
- **Asset building**: Use `laravel_npm_build.php`
- **Permission issues**: Use `laravel_permissions_fixer.php`

### Access All Tools
- **Central dashboard**: Use `laravel_developer_toolkit.php`

⚠️ **SECURITY WARNING**: These scripts are potentially dangerous as they allow executing commands on your server. Use them only temporarily and delete them immediately after use, or protect this directory with password authentication.