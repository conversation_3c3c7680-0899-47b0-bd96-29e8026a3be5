<?php
// Advanced Obfuscator Tool - PHP Processing
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'obfuscate') {
    header('Content-Type: application/json');

    try {
        if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('File upload failed');
        }

        $file = $_FILES['file'];
        $options = json_decode($_POST['options'], true);
        $originalName = $file['name'];
        $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
        $tempPath = $file['tmp_name'];

        // Read file content
        $content = file_get_contents($tempPath);
        if ($content === false) {
            throw new Exception('Failed to read file content');
        }

        // Apply obfuscation based on file type
        $obfuscatedContent = obfuscateContent($content, $extension, $options);

        // Generate output filename
        $outputName = 'obfuscated_' . $originalName;
        $outputPath = __DIR__ . '/' . $outputName;

        // Save obfuscated file
        if (file_put_contents($outputPath, $obfuscatedContent) === false) {
            throw new Exception('Failed to save obfuscated file');
        }

        // Calculate file sizes and reduction
        $originalSize = strlen($content);
        $obfuscatedSize = strlen($obfuscatedContent);
        $reduction = round((($originalSize - $obfuscatedSize) / $originalSize) * 100, 2);

        echo json_encode([
            'success' => true,
            'filename' => $outputName,
            'downloadUrl' => $outputName,
            'size' => $obfuscatedSize,
            'reduction' => $reduction,
            'message' => 'File obfuscated successfully'
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    exit;
}

// Obfuscation functions
function obfuscateContent($content, $extension, $options) {
    switch ($extension) {
        case 'js':
        case 'jsx':
        case 'ts':
        case 'tsx':
            return obfuscateJavaScript($content, $options);
        case 'css':
            return obfuscateCSS($content, $options);
        case 'html':
        case 'htm':
            return obfuscateHTML($content, $options);
        case 'php':
            return obfuscatePHP($content, $options);
        default:
            throw new Exception('Unsupported file type: ' . $extension);
    }
}

function obfuscateJavaScript($content, $options) {
    $obfuscated = $content;

    // Remove comments and minify if enabled
    if ($options['minify']) {
        $obfuscated = preg_replace('/\/\*[\s\S]*?\*\//', '', $obfuscated);
        $obfuscated = preg_replace('/\/\/.*$/m', '', $obfuscated);
        $obfuscated = preg_replace('/\s+/', ' ', $obfuscated);
        $obfuscated = str_replace(['; ', ' {', '} ', ' (', ') ', ' =', '= '], [';', '{', '}', '(', ')', '=', '='], $obfuscated);
    }

    // Variable name scrambling
    if ($options['scrambleVars']) {
        $varPattern = '/\b(var|let|const)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/';
        $varMap = [];

        preg_match_all($varPattern, $obfuscated, $matches);
        foreach ($matches[2] as $varName) {
            if (!isset($varMap[$varName])) {
                $varMap[$varName] = '_0x' . substr(md5($varName . rand()), 0, 6);
            }
        }

        foreach ($varMap as $original => $scrambled) {
            $obfuscated = preg_replace('/\b' . preg_quote($original) . '\b/', $scrambled, $obfuscated);
        }
    }

    // Function name scrambling
    if ($options['scrambleFuncs']) {
        $funcPattern = '/function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/';
        $funcMap = [];

        preg_match_all($funcPattern, $obfuscated, $matches);
        foreach ($matches[1] as $funcName) {
            if (!isset($funcMap[$funcName])) {
                $funcMap[$funcName] = '_0x' . substr(md5($funcName . rand()), 0, 8);
            }
        }

        foreach ($funcMap as $original => $scrambled) {
            $obfuscated = preg_replace('/\b' . preg_quote($original) . '\b/', $scrambled, $obfuscated);
        }
    }

    // String encoding
    if ($options['stringEncode']) {
        $obfuscated = preg_replace_callback('/"([^"]*)"/', function($matches) {
            $encoded = base64_encode($matches[1]);
            return 'atob("' . $encoded . '")';
        }, $obfuscated);

        $obfuscated = preg_replace_callback("/'([^']*)'/", function($matches) {
            $encoded = base64_encode($matches[1]);
            return 'atob("' . $encoded . '")';
        }, $obfuscated);
    }

    // Dead code injection
    if ($options['deadCode']) {
        $deadCodes = [
            'var _0xdead1 = function(){return false;};',
            'var _0xdead2 = Math.random() > 2;',
            'if(false){console.log("dead");}',
            'var _0xdead3 = null || undefined;'
        ];

        foreach ($deadCodes as $deadCode) {
            $insertPos = rand(0, strlen($obfuscated) / 2);
            $obfuscated = substr_replace($obfuscated, $deadCode, $insertPos, 0);
        }
    }

    // Anti-debugging measures
    if ($options['antiDebug']) {
        $antiDebugCode = '
        (function(){
            var _0xcheck = function(){
                if(window.console && (console.firebug || console.table && /firebug/i.test(console.table()))){
                    throw new Error("Debug detected");
                }
                if(typeof console !== "undefined" && console.log){
                    console.log = function(){};
                    console.warn = function(){};
                    console.error = function(){};
                }
            };
            setInterval(_0xcheck, 1000);
        })();';
        $obfuscated = $antiDebugCode . $obfuscated;
    }

    // Control flow obfuscation
    if ($options['controlFlow']) {
        $obfuscated = preg_replace('/if\s*\(([^)]+)\)\s*{/', 'if(!!($1)){', $obfuscated);
        $obfuscated = preg_replace('/while\s*\(([^)]+)\)\s*{/', 'while(!!($1)){', $obfuscated);
    }

    // Integrity checking
    if ($options['integrityCheck']) {
        $hash = md5($obfuscated);
        $integrityCode = '
        (function(){
            var _0xhash = "' . $hash . '";
            var _0xcurrent = btoa(document.documentElement.outerHTML).slice(0,32);
            if(_0xcurrent !== _0xhash.slice(0,32)){
                throw new Error("Integrity check failed");
            }
        })();';
        $obfuscated = $integrityCode . $obfuscated;
    }

    return $obfuscated;
}

function obfuscateCSS($content, $options) {
    $obfuscated = $content;

    // Minify CSS
    if ($options['minify']) {
        $obfuscated = preg_replace('/\/\*[\s\S]*?\*\//', '', $obfuscated);
        $obfuscated = preg_replace('/\s+/', ' ', $obfuscated);
        $obfuscated = str_replace(['; ', ' {', '} ', ': ', ', '], [';', '{', '}', ':', ','], $obfuscated);
        $obfuscated = trim($obfuscated);
    }

    // Class name scrambling
    if ($options['scrambleVars']) {
        $classPattern = '/\.([a-zA-Z_-][a-zA-Z0-9_-]*)/';
        $classMap = [];

        preg_match_all($classPattern, $obfuscated, $matches);
        foreach ($matches[1] as $className) {
            if (!isset($classMap[$className]) && !in_array($className, ['active', 'hover', 'focus', 'visited'])) {
                $classMap[$className] = 'c' . substr(md5($className . rand()), 0, 6);
            }
        }

        foreach ($classMap as $original => $scrambled) {
            $obfuscated = str_replace('.' . $original, '.' . $scrambled, $obfuscated);
        }
    }

    // ID name scrambling
    if ($options['scrambleFuncs']) {
        $idPattern = '/#([a-zA-Z_-][a-zA-Z0-9_-]*)/';
        $idMap = [];

        preg_match_all($idPattern, $obfuscated, $matches);
        foreach ($matches[1] as $idName) {
            if (!isset($idMap[$idName])) {
                $idMap[$idName] = 'i' . substr(md5($idName . rand()), 0, 6);
            }
        }

        foreach ($idMap as $original => $scrambled) {
            $obfuscated = str_replace('#' . $original, '#' . $scrambled, $obfuscated);
        }
    }

    return $obfuscated;
}
?>
<!DOCTYPE html>
<!--
    Advanced Obfuscator Tool
    Copyright (c) 2024 Firoz Anam. All rights reserved.

    PROPRIETARY SOFTWARE - UNAUTHORIZED COPYING PROHIBITED

    This software is protected by copyright law and international treaties.
    Unauthorized reproduction, modification, distribution, or use is strictly prohibited.

    Terms of Use:
    - Personal and commercial use permitted
    - Modification and redistribution prohibited
    - Copyright notice must remain intact
    - Reverse engineering prohibited

    Contact: <EMAIL>
    Website: neurotechsystem.com

    Violation of these terms may result in legal action.
-->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Obfuscator Tool</title>
    <style>
        :root {
            --primary: #ff2d20;
            --primary-dark: #e02417;
            --secondary: #2d3748;
            --secondary-light: #4a5568;
            --accent: #667eea;
            --success: #48bb78;
            --warning: #ed8936;
            --danger: #f56565;
            --info: #4299e1;
            --light: #f7fafc;
            --dark: #1a202c;
            --text: #2d3748;
            --text-light: #718096;
            --border: #e2e8f0;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 20px;
            padding: 25px 40px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-lg);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary) 0%, var(--accent) 100%);
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text);
            margin-bottom: 8px;
        }

        .header p {
            font-size: 1.1rem;
            color: var(--text-light);
            max-width: 600px;
            margin: 0 auto;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .section-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
        }

        .section-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: var(--text);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .file-upload-area {
            border: 2px dashed var(--border);
            border-radius: 12px;
            padding: 40px 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 20px;
        }

        .file-upload-area:hover {
            border-color: var(--primary);
            background: var(--light);
        }

        .file-upload-area.dragover {
            border-color: var(--primary);
            background: var(--light);
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 3rem;
            color: var(--text-light);
            margin-bottom: 15px;
        }

        .upload-text {
            color: var(--text-light);
            margin-bottom: 10px;
        }

        .file-input {
            display: none;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 0.95rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 45, 32, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--secondary) 0%, var(--secondary-light) 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success) 0%, #38a169 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning) 0%, #d69e2e 100%);
        }

        .file-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid var(--border);
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 15px;
            border-bottom: 1px solid var(--border);
            transition: background 0.2s ease;
        }

        .file-item:hover {
            background: var(--light);
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .file-icon {
            font-size: 1.2rem;
        }

        .file-name {
            font-weight: 500;
            color: var(--text);
        }

        .file-size {
            font-size: 0.85rem;
            color: var(--text-light);
        }

        .file-actions {
            display: flex;
            gap: 8px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.8rem;
        }

        .obfuscation-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .option-group {
            background: var(--light);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid var(--border);
        }

        .option-title {
            font-weight: 600;
            color: var(--text);
            margin-bottom: 10px;
            font-size: 0.9rem;
        }

        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-item input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: var(--primary);
        }

        .checkbox-item label {
            font-size: 0.85rem;
            color: var(--text);
            cursor: pointer;
        }

        .progress-section {
            margin-top: 20px;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--border);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary) 0%, var(--accent) 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 0.9rem;
            color: var(--text-light);
            text-align: center;
        }

        .results-section {
            grid-column: span 2;
            margin-top: 20px;
            display: none;
        }

        .result-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            background: var(--light);
            border-radius: 8px;
            margin-bottom: 10px;
            border: 1px solid var(--border);
        }

        .result-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .result-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--success);
        }

        .result-status.error {
            background: var(--danger);
        }

        .result-details {
            display: flex;
            flex-direction: column;
        }

        .result-filename {
            font-weight: 500;
            color: var(--text);
        }

        .result-stats {
            font-size: 0.8rem;
            color: var(--text-light);
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }

        .alert-info {
            background: #e6f3ff;
            border-color: var(--info);
            color: #0c5aa6;
        }

        .alert-warning {
            background: #fff8e1;
            border-color: var(--warning);
            color: #b45309;
        }

        .alert-success {
            background: #f0fff4;
            border-color: var(--success);
            color: #22543d;
        }

        .alert-danger {
            background: #fed7d7;
            border-color: var(--danger);
            color: #c53030;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .obfuscation-options {
                grid-template-columns: 1fr;
            }

            .results-section {
                grid-column: span 1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <h1>🔒 Advanced Obfuscator Tool</h1>
            <p>Powerful code obfuscation for HTML, CSS, JavaScript, and PHP with multi-layer protection and bulk processing capabilities.</p>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- File Upload Section -->
            <div class="section-card">
                <h2 class="section-title">
                    📁 File Selection
                </h2>

                <div class="file-upload-area" id="uploadArea">
                    <div class="upload-icon">📤</div>
                    <div class="upload-text">Drag & drop files here or click to browse</div>
                    <div class="upload-text" style="font-size: 0.9rem;">Supports: .html, .css, .js, .php</div>
                    <input type="file" id="fileInput" class="file-input" multiple accept=".html,.css,.js,.php,.htm,.jsx,.ts,.tsx">
                </div>

                <button class="btn" onclick="document.getElementById('fileInput').click()">
                    📂 Browse Files
                </button>

                <div class="file-list" id="fileList" style="display: none;">
                    <!-- Files will be listed here -->
                </div>

                <div class="progress-section" id="progressSection">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">Processing files...</div>
                </div>
            </div>

            <!-- Obfuscation Options Section -->
            <div class="section-card">
                <h2 class="section-title">
                    ⚙️ Obfuscation Settings
                </h2>

                <div class="obfuscation-options">
                    <div class="option-group">
                        <div class="option-title">🔤 Variable & Function Protection</div>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="scrambleVars" checked>
                                <label for="scrambleVars">Variable name scrambling</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="scrambleFuncs" checked>
                                <label for="scrambleFuncs">Function name obfuscation</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="deadCode" checked>
                                <label for="deadCode">Dead code injection</label>
                            </div>
                        </div>
                    </div>

                    <div class="option-group">
                        <div class="option-title">🔐 String & Flow Protection</div>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="stringEncode" checked>
                                <label for="stringEncode">Multi-layer string encoding</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="controlFlow" checked>
                                <label for="controlFlow">Control flow obfuscation</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="antiDebug" checked>
                                <label for="antiDebug">Anti-debugging measures</label>
                            </div>
                        </div>
                    </div>

                    <div class="option-group">
                        <div class="option-title">🛡️ Advanced Protection</div>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="integrityCheck" checked>
                                <label for="integrityCheck">Integrity checking</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="domainLock">
                                <label for="domainLock">Domain locking</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="timeExpiry">
                                <label for="timeExpiry">Time-based expiry</label>
                            </div>
                        </div>
                    </div>

                    <div class="option-group">
                        <div class="option-title">📦 Output Options</div>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="minify" checked>
                                <label for="minify">Code minification</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="compress" checked>
                                <label for="compress">Compression</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="sourceMap">
                                <label for="sourceMap">Generate source maps</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info">
                    <strong>💡 Pro Tip:</strong> Enable all protection layers for maximum security. Source code will be completely unreadable with all options enabled.
                </div>

                <button class="btn btn-success" id="obfuscateBtn" onclick="startObfuscation()" disabled>
                    🚀 Start Obfuscation
                </button>

                <button class="btn btn-warning" onclick="clearAll()">
                    🗑️ Clear All
                </button>
            </div>
        </div>

        <!-- Results Section -->
        <div class="section-card results-section" id="resultsSection">
            <h2 class="section-title">
                📊 Obfuscation Results
            </h2>
            <div id="resultsList">
                <!-- Results will be displayed here -->
            </div>
        </div>

        <!-- Creator Section -->
        <div class="section-card" style="grid-column: span 2; margin-top: 30px;">
            <h3 style="text-align: center; font-size: 1.5rem; font-weight: 600; margin-bottom: 30px; color: var(--text);">👨‍💻 Created By</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div style="background: var(--light); padding: 20px; border-radius: 12px; border: 1px solid var(--border);">
                    <div style="font-weight: 600; color: var(--primary); margin-bottom: 5px;">Full Stack Developer</div>
                    <div style="color: var(--text-light);">Firoz Anam</div>
                </div>
                <div style="background: var(--light); padding: 20px; border-radius: 12px; border: 1px solid var(--border);">
                    <div style="font-weight: 600; color: var(--primary); margin-bottom: 5px;">📧 Email</div>
                    <div style="color: var(--text-light);"><a href="mailto:<EMAIL>" style="color: var(--text-light); text-decoration: none;"><EMAIL></a></div>
                </div>
                <div style="background: var(--light); padding: 20px; border-radius: 12px; border: 1px solid var(--border);">
                    <div style="font-weight: 600; color: var(--primary); margin-bottom: 5px;">🐙 GitHub</div>
                    <div style="color: var(--text-light);"><a href="https://github.com/firozanam" target="_blank" style="color: var(--text-light); text-decoration: none;">github.com/firozanam</a></div>
                </div>
                <div style="background: var(--light); padding: 20px; border-radius: 12px; border: 1px solid var(--border);">
                    <div style="font-weight: 600; color: var(--primary); margin-bottom: 5px;">💼 LinkedIn</div>
                    <div style="color: var(--text-light);"><a href="https://www.linkedin.com/in/firozanam" target="_blank" style="color: var(--text-light); text-decoration: none;">linkedin.com/in/firozanam</a></div>
                </div>
                <div style="background: var(--light); padding: 20px; border-radius: 12px; border: 1px solid var(--border);">
                    <div style="font-weight: 600; color: var(--primary); margin-bottom: 5px;">📱 WhatsApp</div>
                    <div style="color: var(--text-light);"><a href="https://wa.me/8801788544788" target="_blank" style="color: var(--text-light); text-decoration: none;">+8801788544788</a></div>
                </div>
                <div style="background: var(--light); padding: 20px; border-radius: 12px; border: 1px solid var(--border);">
                    <div style="font-weight: 600; color: var(--primary); margin-bottom: 5px;">🌐 Website</div>
                    <div style="color: var(--text-light);"><a href="https://neurotechsystem.com" target="_blank" style="color: var(--text-light); text-decoration: none;">neurotechsystem.com</a></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedFiles = [];
        let isProcessing = false;

        // File upload handling
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileList = document.getElementById('fileList');
        const obfuscateBtn = document.getElementById('obfuscateBtn');

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });

        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });

        function handleFiles(files) {
            const validExtensions = ['.html', '.css', '.js', '.php', '.htm', '.jsx', '.ts', '.tsx'];

            for (let file of files) {
                const extension = '.' + file.name.split('.').pop().toLowerCase();
                if (validExtensions.includes(extension)) {
                    if (!selectedFiles.find(f => f.name === file.name && f.size === file.size)) {
                        selectedFiles.push(file);
                    }
                }
            }

            updateFileList();
            updateObfuscateButton();
        }

        function updateFileList() {
            if (selectedFiles.length === 0) {
                fileList.style.display = 'none';
                return;
            }

            fileList.style.display = 'block';
            fileList.innerHTML = '';

            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';

                const fileIcon = getFileIcon(file.name);
                const fileSize = formatFileSize(file.size);

                fileItem.innerHTML = `
                    <div class="file-info">
                        <span class="file-icon">${fileIcon}</span>
                        <div>
                            <div class="file-name">${file.name}</div>
                            <div class="file-size">${fileSize}</div>
                        </div>
                    </div>
                    <div class="file-actions">
                        <button class="btn btn-small btn-secondary" onclick="removeFile(${index})">
                            🗑️ Remove
                        </button>
                    </div>
                `;

                fileList.appendChild(fileItem);
            });
        }

        function getFileIcon(filename) {
            const extension = filename.split('.').pop().toLowerCase();
            const icons = {
                'html': '🌐',
                'htm': '🌐',
                'css': '🎨',
                'js': '⚡',
                'jsx': '⚛️',
                'ts': '📘',
                'tsx': '📘',
                'php': '🐘'
            };
            return icons[extension] || '📄';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function removeFile(index) {
            selectedFiles.splice(index, 1);
            updateFileList();
            updateObfuscateButton();
        }

        function updateObfuscateButton() {
            obfuscateBtn.disabled = selectedFiles.length === 0 || isProcessing;
        }

        function clearAll() {
            selectedFiles = [];
            updateFileList();
            updateObfuscateButton();
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('progressSection').style.display = 'none';
        }

        async function startObfuscation() {
            if (selectedFiles.length === 0 || isProcessing) return;

            isProcessing = true;
            updateObfuscateButton();

            const progressSection = document.getElementById('progressSection');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const resultsSection = document.getElementById('resultsSection');
            const resultsList = document.getElementById('resultsList');

            progressSection.style.display = 'block';
            resultsSection.style.display = 'block';
            resultsList.innerHTML = '';

            const options = getObfuscationOptions();
            let processedCount = 0;

            for (let i = 0; i < selectedFiles.length; i++) {
                const file = selectedFiles[i];
                const progress = ((i + 1) / selectedFiles.length) * 100;

                progressFill.style.width = progress + '%';
                progressText.textContent = `Processing ${file.name}... (${i + 1}/${selectedFiles.length})`;

                try {
                    const result = await obfuscateFile(file, options);
                    displayResult(file, result, true);
                    processedCount++;
                } catch (error) {
                    displayResult(file, { error: error.message }, false);
                }
            }

            progressText.textContent = `Completed! Processed ${processedCount}/${selectedFiles.length} files.`;
            isProcessing = false;
            updateObfuscateButton();
        }

        function getObfuscationOptions() {
            return {
                scrambleVars: document.getElementById('scrambleVars').checked,
                scrambleFuncs: document.getElementById('scrambleFuncs').checked,
                deadCode: document.getElementById('deadCode').checked,
                stringEncode: document.getElementById('stringEncode').checked,
                controlFlow: document.getElementById('controlFlow').checked,
                antiDebug: document.getElementById('antiDebug').checked,
                integrityCheck: document.getElementById('integrityCheck').checked,
                domainLock: document.getElementById('domainLock').checked,
                timeExpiry: document.getElementById('timeExpiry').checked,
                minify: document.getElementById('minify').checked,
                compress: document.getElementById('compress').checked,
                sourceMap: document.getElementById('sourceMap').checked
            };
        }

        async function obfuscateFile(file, options) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('options', JSON.stringify(options));
            formData.append('action', 'obfuscate');

            const response = await fetch(window.location.href, {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                return result;
            } else {
                throw new Error(result.error || 'Obfuscation failed');
            }
        }

        function displayResult(file, result, success) {
            const resultsList = document.getElementById('resultsList');
            const resultItem = document.createElement('div');
            resultItem.className = 'result-item';

            const statusClass = success ? '' : 'error';
            const statusIcon = success ? '✅' : '❌';
            const downloadButton = success ? `
                <button class="btn btn-small btn-success" onclick="downloadFile('${result.filename}', '${result.downloadUrl}')">
                    💾 Download
                </button>
            ` : '';

            const stats = success ?
                `Original: ${formatFileSize(file.size)} | Obfuscated: ${formatFileSize(result.size)} | Reduction: ${result.reduction}%` :
                `Error: ${result.error}`;

            resultItem.innerHTML = `
                <div class="result-info">
                    <div class="result-status ${statusClass}"></div>
                    <div class="result-details">
                        <div class="result-filename">${statusIcon} ${file.name} → obfuscated_${file.name}</div>
                        <div class="result-stats">${stats}</div>
                    </div>
                </div>
                <div class="file-actions">
                    ${downloadButton}
                </div>
            `;

            resultsList.appendChild(resultItem);
        }

        function downloadFile(filename, url) {
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Initialize tooltips and help text
        document.addEventListener('DOMContentLoaded', function() {
            // Add tooltips to checkboxes
            const tooltips = {
                'scrambleVars': 'Replaces variable names with random strings',
                'scrambleFuncs': 'Obfuscates function names and signatures',
                'deadCode': 'Injects non-functional code to confuse analysis',
                'stringEncode': 'Encodes strings using multiple layers of encoding',
                'controlFlow': 'Restructures code flow to make it harder to follow',
                'antiDebug': 'Adds measures to prevent debugging and analysis',
                'integrityCheck': 'Validates code integrity at runtime',
                'domainLock': 'Restricts code execution to specific domains',
                'timeExpiry': 'Adds time-based expiration to the code',
                'minify': 'Removes whitespace and comments',
                'compress': 'Applies compression algorithms',
                'sourceMap': 'Generates source maps for debugging'
            };

            Object.keys(tooltips).forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.title = tooltips[id];
                }
            });
        });
    </script>

<?php
function obfuscateHTML($content, $options) {
    $obfuscated = $content;

    // Minify HTML
    if ($options['minify']) {
        $obfuscated = preg_replace('/<!--[\s\S]*?-->/', '', $obfuscated);
        $obfuscated = preg_replace('/\s+/', ' ', $obfuscated);
        $obfuscated = preg_replace('/>\s+</', '><', $obfuscated);
        $obfuscated = trim($obfuscated);
    }

    // Scramble class and ID attributes
    if ($options['scrambleVars']) {
        $classPattern = '/class=["\']([^"\']*)["\']/';;
        $classMap = [];

        preg_match_all($classPattern, $obfuscated, $matches);
        foreach ($matches[1] as $classString) {
            $classes = explode(' ', $classString);
            foreach ($classes as $className) {
                $className = trim($className);
                if ($className && !isset($classMap[$className])) {
                    $classMap[$className] = 'c' . substr(md5($className . rand()), 0, 6);
                }
            }
        }

        foreach ($classMap as $original => $scrambled) {
            $obfuscated = preg_replace('/\b' . preg_quote($original) . '\b/', $scrambled, $obfuscated);
        }
    }

    // Scramble ID attributes
    if ($options['scrambleFuncs']) {
        $idPattern = '/id=["\']([^"\']*)["\']/';;
        $idMap = [];

        preg_match_all($idPattern, $obfuscated, $matches);
        foreach ($matches[1] as $idName) {
            if (!isset($idMap[$idName])) {
                $idMap[$idName] = 'i' . substr(md5($idName . rand()), 0, 6);
            }
        }

        foreach ($idMap as $original => $scrambled) {
            $obfuscated = str_replace('id="' . $original . '"', 'id="' . $scrambled . '"', $obfuscated);
            $obfuscated = str_replace("id='" . $original . "'", "id='" . $scrambled . "'", $obfuscated);
        }
    }

    // Encode inline JavaScript
    if ($options['stringEncode']) {
        $obfuscated = preg_replace_callback('/<script[^>]*>(.*?)<\/script>/s', function($matches) {
            $jsContent = $matches[1];
            $encoded = base64_encode($jsContent);
            return '<script>eval(atob("' . $encoded . '"));</script>';
        }, $obfuscated);
    }

    // Add anti-debugging to inline scripts
    if ($options['antiDebug']) {
        $antiDebugScript = '<script>(function(){if(window.console){console.log=console.warn=console.error=function(){};}})();</script>';
        $obfuscated = preg_replace('/<head[^>]*>/', '$0' . $antiDebugScript, $obfuscated);
    }

    return $obfuscated;
}

function obfuscatePHP($content, $options) {
    // Check if this is a mixed HTML/PHP file
    if (preg_match('/^\s*<!DOCTYPE|^\s*<html/i', $content)) {
        return obfuscateMixedHTMLPHP($content, $options);
    }

    $obfuscated = $content;

    // Remove comments and minify (only for pure PHP files)
    if ($options['minify']) {
        $obfuscated = preg_replace('/\/\*[\s\S]*?\*\//', '', $obfuscated);
        $obfuscated = preg_replace('/\/\/.*$/m', '', $obfuscated);
        $obfuscated = preg_replace('/#.*$/m', '', $obfuscated);
        $obfuscated = preg_replace('/\s+/', ' ', $obfuscated);
        $obfuscated = str_replace(['; ', ' {', '} ', ' (', ') '], [';', '{', '}', '(', ')'], $obfuscated);
    }

    // Variable name scrambling (only within PHP tags)
    if ($options['scrambleVars']) {
        $varPattern = '/\$([a-zA-Z_][a-zA-Z0-9_]*)/';
        $varMap = [];

        preg_match_all($varPattern, $obfuscated, $matches);
        foreach ($matches[1] as $varName) {
            if (!isset($varMap[$varName]) && !in_array($varName, ['_GET', '_POST', '_SESSION', '_COOKIE', '_SERVER', '_FILES', '_ENV', '_REQUEST', 'GLOBALS'])) {
                $varMap[$varName] = '_0x' . substr(md5($varName . rand()), 0, 6);
            }
        }

        foreach ($varMap as $original => $scrambled) {
            $obfuscated = preg_replace('/\$' . preg_quote($original) . '\b/', '$' . $scrambled, $obfuscated);
        }
    }

    // Function name scrambling
    if ($options['scrambleFuncs']) {
        $funcPattern = '/function\s+([a-zA-Z_][a-zA-Z0-9_]*)/';
        $funcMap = [];

        preg_match_all($funcPattern, $obfuscated, $matches);
        foreach ($matches[1] as $funcName) {
            if (!isset($funcMap[$funcName]) && !function_exists($funcName)) {
                $funcMap[$funcName] = '_0x' . substr(md5($funcName . rand()), 0, 8);
            }
        }

        foreach ($funcMap as $original => $scrambled) {
            $obfuscated = preg_replace('/\b' . preg_quote($original) . '\b/', $scrambled, $obfuscated);
        }
    }

    // String encoding (only for PHP strings, not HTML content)
    if ($options['stringEncode']) {
        // Only encode strings within PHP tags
        $obfuscated = preg_replace_callback('/(<\?php.*?\?>)/s', function($matches) {
            $phpCode = $matches[1];

            $phpCode = preg_replace_callback('/"([^"]*)"/', function($m) {
                if (strlen($m[1]) > 0) {
                    $encoded = base64_encode($m[1]);
                    return 'base64_decode("' . $encoded . '")';
                }
                return $m[0];
            }, $phpCode);

            $phpCode = preg_replace_callback("/'([^']*)'/", function($m) {
                if (strlen($m[1]) > 0) {
                    $encoded = base64_encode($m[1]);
                    return 'base64_decode("' . $encoded . '")';
                }
                return $m[0];
            }, $phpCode);

            return $phpCode;
        }, $obfuscated);
    }

    // Dead code injection (only within PHP sections)
    if ($options['deadCode']) {
        $deadCodes = [
            '$_0xdead1 = function(){return false;};',
            '$_0xdead2 = rand() > 999999;',
            'if(false){echo "dead";}',
            '$_0xdead3 = null;'
        ];

        // Insert dead code only in PHP sections
        $obfuscated = preg_replace_callback('/(<\?php)(.*?)(\?>)/s', function($matches) use ($deadCodes) {
            $phpContent = $matches[2];
            $deadCode = $deadCodes[array_rand($deadCodes)];
            return $matches[1] . $deadCode . $phpContent . $matches[3];
        }, $obfuscated);
    }

    // Control flow obfuscation
    if ($options['controlFlow']) {
        $obfuscated = preg_replace('/if\s*\(([^)]+)\)\s*{/', 'if(!!($1)){', $obfuscated);
        $obfuscated = preg_replace('/while\s*\(([^)]+)\)\s*{/', 'while(!!($1)){', $obfuscated);
    }

    return $obfuscated;
}

function obfuscateMixedHTMLPHP($content, $options) {
    $obfuscated = $content;

    // First, extract and protect PHP sections
    $phpSections = [];
    $phpCounter = 0;

    $obfuscated = preg_replace_callback('/(<\?php.*?\?>)/s', function($matches) use (&$phpSections, &$phpCounter) {
        $placeholder = "___PHP_SECTION_" . $phpCounter . "___";
        $phpSections[$placeholder] = $matches[1];
        $phpCounter++;
        return $placeholder;
    }, $obfuscated);

    // Apply HTML obfuscation to the HTML parts
    $obfuscated = obfuscateHTML($obfuscated, $options);

    // Now process each PHP section separately
    foreach ($phpSections as $placeholder => $phpCode) {
        // Apply PHP-specific obfuscation to this section
        $obfuscatedPHP = obfuscatePurePHP($phpCode, $options);
        $obfuscated = str_replace($placeholder, $obfuscatedPHP, $obfuscated);
    }

    return $obfuscated;
}

function obfuscatePurePHP($phpCode, $options) {
    $obfuscated = $phpCode;

    // Variable name scrambling within this PHP section
    if ($options['scrambleVars']) {
        $varPattern = '/\$([a-zA-Z_][a-zA-Z0-9_]*)/';
        $varMap = [];

        preg_match_all($varPattern, $obfuscated, $matches);
        foreach ($matches[1] as $varName) {
            if (!isset($varMap[$varName]) && !in_array($varName, ['_GET', '_POST', '_SESSION', '_COOKIE', '_SERVER', '_FILES', '_ENV', '_REQUEST', 'GLOBALS'])) {
                $varMap[$varName] = '_0x' . substr(md5($varName . rand()), 0, 6);
            }
        }

        foreach ($varMap as $original => $scrambled) {
            $obfuscated = preg_replace('/\$' . preg_quote($original) . '\b/', '$' . $scrambled, $obfuscated);
        }
    }

    // Function name scrambling
    if ($options['scrambleFuncs']) {
        $funcPattern = '/function\s+([a-zA-Z_][a-zA-Z0-9_]*)/';
        $funcMap = [];

        preg_match_all($funcPattern, $obfuscated, $matches);
        foreach ($matches[1] as $funcName) {
            if (!isset($funcMap[$funcName]) && !function_exists($funcName)) {
                $funcMap[$funcName] = '_0x' . substr(md5($funcName . rand()), 0, 8);
            }
        }

        foreach ($funcMap as $original => $scrambled) {
            $obfuscated = preg_replace('/\b' . preg_quote($original) . '\b/', $scrambled, $obfuscated);
        }
    }

    // String encoding for PHP strings only
    if ($options['stringEncode']) {
        $obfuscated = preg_replace_callback('/"([^"]*)"/', function($matches) {
            if (strlen($matches[1]) > 0 && !preg_match('/^[a-zA-Z_][a-zA-Z0-9_]*$/', $matches[1])) {
                $encoded = base64_encode($matches[1]);
                return 'base64_decode("' . $encoded . '")';
            }
            return $matches[0];
        }, $obfuscated);

        $obfuscated = preg_replace_callback("/'([^']*)'/", function($matches) {
            if (strlen($matches[1]) > 0 && !preg_match('/^[a-zA-Z_][a-zA-Z0-9_]*$/', $matches[1])) {
                $encoded = base64_encode($matches[1]);
                return 'base64_decode("' . $encoded . '")';
            }
            return $matches[0];
        }, $obfuscated);
    }

    return $obfuscated;
}
?>
</body>
</html>
