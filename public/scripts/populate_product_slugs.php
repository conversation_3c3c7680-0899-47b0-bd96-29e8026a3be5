<?php
/**
 * <PERSON>vel Product Slug Generator for cPanel Shared Hosting
 * 
 * This script populates slugs for products that don't have one
 * in Laravel applications running on cPanel shared hosting
 * where terminal access is limited.
 * 
 * ⚠️ SECURITY WARNING: This script can modify your database.
 * Delete it immediately after use.
 * 
 * Usage: Upload this script to your public/scripts directory and access it via browser.
 */

// Set execution time limit to 300 seconds (5 minutes)
set_time_limit(300);

// Start output buffering
ob_start();

// Define the base directory (Laravel root)
$baseDir = dirname(dirname(__DIR__));

// Require the autoloader
require $baseDir . '/vendor/autoload.php';

// Load the environment file
$dotenv = Dotenv\Dotenv::createImmutable($baseDir);
$dotenv->load();

// Bootstrap Laravel
$app = require_once $baseDir . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laravel Product Slug Generator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #2d3748;
        }
        .success {
            color: #38a169;
            background-color: #f0fff4;
            border-left: 4px solid #38a169;
            padding: 8px 12px;
            margin: 8px 0;
        }
        .warning {
            color: #d69e2e;
            background-color: #fffaf0;
            border-left: 4px solid #d69e2e;
            padding: 8px 12px;
            margin: 8px 0;
        }
        .error {
            color: #e53e3e;
            background-color: #fff5f5;
            border-left: 4px solid #e53e3e;
            padding: 8px 12px;
            margin: 8px 0;
        }
        .info {
            color: #3182ce;
            background-color: #ebf8ff;
            border-left: 4px solid #3182ce;
            padding: 8px 12px;
            margin: 8px 0;
        }
        code {
            background-color: #f7fafc;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: Menlo, Monaco, Consolas, "Liberation Mono", monospace;
        }
        pre {
            background-color: #f7fafc;
            padding: 12px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .command-output {
            background-color: #1a202c;
            color: #e2e8f0;
            padding: 12px;
            border-radius: 5px;
            font-family: Menlo, Monaco, Consolas, "Liberation Mono", monospace;
            margin-top: 10px;
            white-space: pre-wrap;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #e2e8f0;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f7fafc;
        }
        tr:nth-child(even) {
            background-color: #f7fafc;
        }
        .security-warning {
            background-color: #742a2a;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .progress-container {
            width: 100%;
            background-color: #e2e8f0;
            border-radius: 5px;
            margin: 10px 0;
        }
        .progress-bar {
            height: 20px;
            background-color: #4299e1;
            border-radius: 5px;
            width: 0%;
            transition: width 0.3s;
        }
        button {
            background-color: #4299e1;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #3182ce;
        }
    </style>
</head>
<body>
    <div class="security-warning">
        ⚠️ SECURITY WARNING: This script can modify your database. Delete this file immediately after use!
    </div>
    <h1>Laravel Product Slug Generator</h1>
';

// Check if Product model exists
if (!class_exists('\\App\\Models\\Product')) {
    echo '<div class="error">
        <h2>Error: Product Model Not Found</h2>
        <p>The Product model class could not be found. Make sure:</p>
        <ul>
            <li>The model exists at <code>app/Models/Product.php</code></li>
            <li>The namespace is <code>App\\Models</code></li>
        </ul>
        <p>If your model is in a different location, you will need to modify this script.</p>
    </div>';
    exit;
}

// Verify database connection
try {
    \Illuminate\Support\Facades\DB::connection()->getPdo();
    echo '<div class="success">Database connection successful.</div>';
    
    // Debug information about the Product model
    echo '<div class="info">
        <h3>Product Model Information</h3>
        <ul>
            <li>Model exists: ' . (class_exists('\\App\\Models\\Product') ? 'Yes' : 'No') . '</li>
            <li>Fillable attributes: ' . htmlspecialchars(json_encode(\App\Models\Product::make()->getFillable())) . '</li>
            <li>Has slug in fillable: ' . (in_array('slug', \App\Models\Product::make()->getFillable()) ? 'Yes' : 'No') . '</li>
            <li>Database table: ' . \App\Models\Product::make()->getTable() . '</li>
        </ul>
    </div>';
    
    // Check if the products table has a slug column
    $hasSlugColumn = \Illuminate\Support\Facades\Schema::hasColumn('products', 'slug');
    echo '<div class="' . ($hasSlugColumn ? 'success' : 'error') . '">
        Products table ' . ($hasSlugColumn ? 'has' : 'does not have') . ' a slug column.
    </div>';
    
} catch (\Exception $e) {
    echo '<div class="error">
        <h2>Database Connection Error</h2>
        <p>' . htmlspecialchars($e->getMessage()) . '</p>
        <p>Please check your database credentials in the .env file.</p>
    </div>';
    exit;
}

// Function to generate a unique slug
function generateUniqueSlug($name, $productId = null) {
    $slug = \Illuminate\Support\Str::slug($name);
    $originalSlug = $slug;
    $counter = 1;
    
    // Check if slug exists for any other product
    $query = \App\Models\Product::where('slug', $slug);
    if ($productId) {
        $query->where('id', '!=', $productId);
    }
    
    while ($query->exists()) {
        $slug = "{$originalSlug}-{$counter}";
        $counter++;
        $query = \App\Models\Product::where('slug', $slug);
        if ($productId) {
            $query->where('id', '!=', $productId);
        }
    }
    
    return $slug;
}

// Check if user confirmed the action
if (isset($_POST['action']) && $_POST['action'] === 'populate-slugs') {
    echo '<div class="progress-container">
        <div class="progress-bar" id="progress-bar"></div>
    </div>';
    echo '<div id="log">';
    
    try {
        // Get products without slugs
        $products = \App\Models\Product::whereNull('slug')
            ->orWhere('slug', '')
            ->get();
        
        $count = $products->count();
        echo "<div class='info'>Found {$count} products without slugs.</div>";
        
        if ($count === 0) {
            echo "<div class='success'>All products already have slugs. Nothing to do.</div>";
        } else {
            // Add option to use direct DB update
            $useDirectDb = isset($_POST['use_direct_db']) && $_POST['use_direct_db'] === 'yes';
            
            echo "<div class='info'>" . 
                ($useDirectDb ? "Using direct database updates" : "Using Eloquent model updates") . 
                "</div>";
            
            echo "<h2>Processing Products</h2>";
            echo "<table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Generated Slug</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>";
            
            $updated = 0;
            $errors = 0;
            $counter = 0;
            
            foreach ($products as $product) {
                $counter++;
                $progress = round(($counter / $count) * 100);
                echo "<script>document.getElementById('progress-bar').style.width = '{$progress}%';</script>";
                flush();
                
                try {
                    $slug = generateUniqueSlug($product->name, $product->id);
                    
                    // Debug information before saving
                    $debugInfo = "Before save: ID={$product->id}, Name=" . htmlspecialchars($product->name) . ", Slug={$slug}";
                    
                    $saveResult = false;
                    
                    if ($useDirectDb) {
                        // Direct database update
                        $saveResult = \Illuminate\Support\Facades\DB::table('products')
                            ->where('id', $product->id)
                            ->update(['slug' => $slug]);
                        
                        $debugInfo .= "<br>Update method: Direct DB";
                    } else {
                        // Eloquent model update
                        $product->slug = $slug;
                        $saveResult = $product->save();
                        $debugInfo .= "<br>Update method: Eloquent";
                    }
                    
                    // Check if save was successful and get updated product from database
                    if ($saveResult) {
                        // Refresh the product from database to verify the save
                        $refreshedProduct = \App\Models\Product::find($product->id);
                        $debugInfo .= "<br>After save: Result={$saveResult}, DB Slug=" . ($refreshedProduct ? $refreshedProduct->slug : 'Product not found');
                        
                        echo "<tr>
                            <td>{$product->id}</td>
                            <td>" . htmlspecialchars($product->name) . "</td>
                            <td>{$slug}</td>
                            <td><span style='color: #38a169;'>Updated</span><br><small>{$debugInfo}</small></td>
                        </tr>";
                        
                        $updated++;
                    } else {
                        echo "<tr>
                            <td>{$product->id}</td>
                            <td>" . htmlspecialchars($product->name) . "</td>
                            <td>{$slug}</td>
                            <td><span style='color: #e53e3e;'>Failed to save</span><br><small>{$debugInfo}</small></td>
                        </tr>";
                        
                        $errors++;
                    }
                } catch (\Exception $e) {
                    echo "<tr>
                        <td>{$product->id}</td>
                        <td>" . htmlspecialchars($product->name) . "</td>
                        <td>-</td>
                        <td><span style='color: #e53e3e;'>Error: " . htmlspecialchars($e->getMessage()) . "</span><br>
                        <small>Trace: " . htmlspecialchars($e->getTraceAsString()) . "</small></td>
                    </tr>";
                    
                    $errors++;
                }
                
                flush();
            }
            
            echo "</tbody></table>";
            
            echo "<div class='success'>Slug population completed: {$updated} products updated, {$errors} errors encountered.</div>";
        }
    } catch (\Exception $e) {
        echo "<div class='error'>Error: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    
    echo '</div>';
    
    echo '<a href="' . $_SERVER['PHP_SELF'] . '"><button>Back</button></a>';
} else {
    // Display confirmation form
    echo '
    <div class="info">
        This script will generate and populate slugs for all products in your database that don\'t have a slug.
        <br>Slugs are used in your product URLs: /products/your-product-slug
    </div>
    
    <form method="post">
        <input type="hidden" name="action" value="populate-slugs">
        <div style="margin-bottom: 15px;">
            <label>
                <input type="checkbox" name="use_direct_db" value="yes"> 
                Use direct database updates (try this if Eloquent updates aren\'t working)
            </label>
        </div>
        <button type="submit">Generate Missing Product Slugs</button>
    </form>
    ';
}

echo '
</body>
</html>'; 