<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PaddleConfigController extends Controller
{
    /**
     * Show the Paddle configuration form.
     */
    public function configure()
    {
        // In a real implementation, you would fetch this from the database
        $paddleConfig = [
            'id' => 1,
            'is_enabled' => true,
            'environment' => 'sandbox',
            'vendor_id' => '12345',
            'vendor_auth_code' => '',
            'public_key' => '',
            'webhook_secret' => '',
            'default_currency' => 'USD',
            'supported_currencies' => ['USD', 'EUR', 'GBP'],
        ];

        $webhookUrl = url('/webhooks/paddle');

        return Inertia::render('admin/payment-gateways/paddle/Configure', [
            'paddleConfig' => $paddleConfig,
            'webhookUrl' => $webhookUrl
        ]);
    }

    /**
     * Store the Paddle configuration.
     */
    public function store(Request $request)
    {
        $request->validate([
            'is_enabled' => 'required|boolean',
            'environment' => 'required|in:sandbox,production',
            'vendor_id' => 'required|string|max:255',
            'vendor_auth_code' => 'required|string|max:255',
            'public_key' => 'required|string',
            'webhook_secret' => 'nullable|string|max:255',
            'default_currency' => 'required|string|in:USD,EUR,GBP',
            'supported_currencies' => 'required|array',
            'supported_currencies.*' => 'string|in:USD,EUR,GBP,CAD,AUD,JPY,CHF,SEK,NOK,DKK',
        ]);

        // In a real implementation, you would save this to the database
        // For now, we'll just redirect back with a success message
        
        return redirect()
            ->route('admin.payment-gateways.paddle.configure')
            ->with('success', 'Paddle configuration saved successfully!');
    }
}
