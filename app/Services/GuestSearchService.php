<?php

namespace App\Services;

use App\Models\Part;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class GuestSearchService
{
    /**
     * Perform a search for guest users with limitations.
     */
    public function searchParts(Request $request): array
    {
        $query = $request->get('q', '');
        $searchType = $request->get('type', 'all');
        $deviceId = $request->get('device_id', '');

        // Validate device ID
        if (empty($deviceId)) {
            return [
                'error' => 'Device identification required',
                'message' => 'Please enable JavaScript to use the search feature.',
            ];
        }

        // Check if device has already performed a search
        $cacheKey = "guest_search_{$deviceId}";
        $hasSearched = Cache::get($cacheKey, false);

        if ($hasSearched) {
            return [
                'error' => 'Search limit exceeded',
                'message' => 'You have used your free search. Please sign up to continue searching our mobile parts database.',
                'signup_url' => route('register'),
                'login_url' => route('login'),
                'limit_reached' => true,
            ];
        }

        // Perform the search
        $results = $this->performSearch($query, $searchType);

        // Mark device as having searched (expires in 24 hours)
        Cache::put($cacheKey, true, now()->addHours(24));

        return [
            'results' => $results,
            'query' => $query,
            'search_type' => $searchType,
            'total' => $results->total(),
            'per_page' => $results->perPage(),
            'current_page' => $results->currentPage(),
            'last_page' => $results->lastPage(),
            'guest_search_used' => true,
            'message' => 'This was your free search. Sign up to get unlimited access to our mobile parts database.',
            'signup_url' => route('register'),
        ];
    }

    /**
     * Check if a device has already used their free search.
     */
    public function hasDeviceSearched(string $deviceId): bool
    {
        $cacheKey = "guest_search_{$deviceId}";
        return Cache::get($cacheKey, false);
    }

    /**
     * Get search status for a device.
     */
    public function getSearchStatus(string $deviceId): array
    {
        $hasSearched = $this->hasDeviceSearched($deviceId);

        return [
            'has_searched' => $hasSearched,
            'searches_remaining' => $hasSearched ? 0 : 1,
            'message' => $hasSearched 
                ? 'You have used your free search. Sign up for unlimited access.'
                : 'You have 1 free search available.',
        ];
    }

    /**
     * Perform the actual search query.
     */
    private function performSearch(string $query, string $searchType)
    {
        $partsQuery = Part::query()
            ->with(['category', 'models.brand'])
            ->where('is_active', true);

        // Apply search based on type
        switch ($searchType) {
            case 'category':
                $partsQuery->whereHas('category', function ($q) use ($query) {
                    $q->where('name', 'like', "%{$query}%");
                });
                break;
            case 'model':
                $partsQuery->whereHas('models', function ($q) use ($query) {
                    $q->where('name', 'like', "%{$query}%")
                      ->orWhereHas('brand', function ($brandQuery) use ($query) {
                          $brandQuery->where('name', 'like', "%{$query}%");
                      });
                });
                break;
            case 'part_name':
                $partsQuery->where('name', 'like', "%{$query}%")
                          ->orWhere('part_number', 'like', "%{$query}%");
                break;
            default:
                // Search all fields
                $partsQuery->where(function ($q) use ($query) {
                    $q->where('name', 'like', "%{$query}%")
                      ->orWhere('part_number', 'like', "%{$query}%")
                      ->orWhere('description', 'like', "%{$query}%")
                      ->orWhereHas('category', function ($categoryQuery) use ($query) {
                          $categoryQuery->where('name', 'like', "%{$query}%");
                      })
                      ->orWhereHas('models', function ($modelQuery) use ($query) {
                          $modelQuery->where('name', 'like', "%{$query}%")
                                    ->orWhereHas('brand', function ($brandQuery) use ($query) {
                                        $brandQuery->where('name', 'like', "%{$query}%");
                                    });
                      });
                });
        }

        // Limit results for guest users
        return $partsQuery->paginate(10);
    }

    /**
     * Get available filters for guest users (limited set).
     */
    public function getAvailableFilters(): array
    {
        return [
            'categories' => \App\Models\Category::where('is_active', true)
                ->orderBy('name')
                ->limit(10)
                ->get(['id', 'name']),
            'brands' => \App\Models\Brand::where('is_active', true)
                ->orderBy('name')
                ->limit(10)
                ->get(['id', 'name']),
        ];
    }
}
