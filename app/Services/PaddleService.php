<?php

namespace App\Services;

use App\Models\User;
use App\Models\PricingPlan;
use Illuminate\Support\Facades\Log;
use Paddle\SDK\Client;
use Paddle\SDK\Entities\Customer;
use Paddle\SDK\Entities\Price;
use Paddle\SDK\Entities\Subscription;
use Paddle\SDK\Entities\Transaction;
use Paddle\SDK\Exceptions\ApiError;
use Paddle\SDK\Exceptions\SdkExceptions\MalformedResponse;
use Paddle\SDK\Resources\Customers\Operations\CreateCustomer;
use Paddle\SDK\Resources\Customers\Operations\ListCustomers;
use Paddle\SDK\Resources\Prices\Operations\ListPrices;
use Paddle\SDK\Resources\Transactions\Operations\CreateTransaction;
use Paddle\SDK\Resources\Transactions\Operations\Create\TransactionCreateItem;

class PaddleService
{
    protected ?Client $paddle;

    public function __construct(?Client $paddle)
    {
        $this->paddle = $paddle;
    }

    /**
     * Get or create a Paddle customer for the given user.
     */
    public function getOrCreateCustomer(User $user): ?Customer
    {
        // Handle development mode
        if ($this->isDevelopmentMode()) {
            return $this->createMockCustomer($user);
        }

        if (!$this->paddle) {
            Log::error('Paddle client not available', [
                'user_id' => $user->id,
                'is_configured' => $this->isConfigured(),
                'is_development_mode' => $this->isDevelopmentMode(),
            ]);
            return null;
        }

        try {
            // If user already has a Paddle customer ID, try to get the customer
            if ($user->paddle_customer_id) {
                try {
                    return $this->paddle->customers->get($user->paddle_customer_id);
                } catch (ApiError $e) {
                    // Customer not found, create a new one
                    Log::warning('Paddle customer not found, creating new one', [
                        'user_id' => $user->id,
                        'paddle_customer_id' => $user->paddle_customer_id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Create new customer
            $operation = new CreateCustomer(
                email: $user->email,
                name: $user->name
            );

            $customer = $this->paddle->customers->create($operation);

            // Update user with Paddle customer ID
            $user->update(['paddle_customer_id' => $customer->id]);

            Log::info('Created new Paddle customer', [
                'user_id' => $user->id,
                'paddle_customer_id' => $customer->id
            ]);

            return $customer;
        } catch (ApiError | MalformedResponse $e) {
            // Check if the error is due to email conflict (customer already exists)
            if (str_contains($e->getMessage(), 'customer email conflicts')) {
                Log::info('Customer with email already exists, attempting to find existing customer', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'error' => $e->getMessage()
                ]);

                // Extract customer ID from error message
                if (preg_match('/customer of id (ctm_[a-zA-Z0-9]+)/', $e->getMessage(), $matches)) {
                    $existingCustomerId = $matches[1];

                    Log::info('Extracted customer ID from error message', [
                        'user_id' => $user->id,
                        'email' => $user->email,
                        'existing_customer_id' => $existingCustomerId
                    ]);

                    // Try to get the existing customer directly by ID
                    try {
                        $existingCustomer = $this->paddle->customers->get($existingCustomerId);

                        // Update user with the existing Paddle customer ID
                        $user->update(['paddle_customer_id' => $existingCustomer->id]);

                        Log::info('Found and linked existing Paddle customer by ID', [
                            'user_id' => $user->id,
                            'paddle_customer_id' => $existingCustomer->id,
                            'email' => $user->email,
                            'customer_email' => $existingCustomer->email
                        ]);

                        return $existingCustomer;
                    } catch (ApiError | MalformedResponse $getError) {
                        Log::error('Failed to get existing Paddle customer by ID', [
                            'user_id' => $user->id,
                            'email' => $user->email,
                            'existing_customer_id' => $existingCustomerId,
                            'get_error' => $getError->getMessage()
                        ]);
                    }
                }

                // Fallback: Try to find the existing customer by email search
                try {
                    Log::info('Attempting to search for existing Paddle customer by email', [
                        'user_id' => $user->id,
                        'email' => $user->email
                    ]);

                    $listOperation = new ListCustomers(
                        pager: null,
                        ids: [],
                        statuses: [],
                        search: '',
                        emails: [$user->email]
                    );
                    $customers = $this->paddle->customers->list($listOperation);

                    Log::info('Customer search completed', [
                        'user_id' => $user->id,
                        'email' => $user->email,
                        'customers_found' => count($customers->data ?? []),
                        'customers_data' => !empty($customers->data) ? array_map(function($c) { return ['id' => $c->id, 'email' => $c->email]; }, $customers->data) : []
                    ]);

                    if (!empty($customers->data)) {
                        $existingCustomer = $customers->data[0];

                        // Update user with the existing Paddle customer ID
                        $user->update(['paddle_customer_id' => $existingCustomer->id]);

                        Log::info('Found and linked existing Paddle customer by search', [
                            'user_id' => $user->id,
                            'paddle_customer_id' => $existingCustomer->id,
                            'email' => $user->email
                        ]);

                        return $existingCustomer;
                    } else {
                        Log::warning('No existing customer found with email search', [
                            'user_id' => $user->id,
                            'email' => $user->email
                        ]);
                    }
                } catch (ApiError | MalformedResponse $searchError) {
                    Log::error('Failed to search for existing Paddle customer', [
                        'user_id' => $user->id,
                        'email' => $user->email,
                        'search_error' => $searchError->getMessage()
                    ]);
                }
            }

            Log::error('Failed to get or create Paddle customer', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'is_configured' => $this->isConfigured(),
                'config_debug' => [
                    'api_key_present' => !empty(config('paddle.api_key')),
                    'api_key_is_placeholder' => $this->isPlaceholderValue(config('paddle.api_key', '')),
                ]
            ]);
            return null;
        }
    }

    /**
     * Get price information from Paddle.
     */
    public function getPrice(string $priceId): ?Price
    {
        if (!$this->paddle) {
            return null;
        }

        try {
            return $this->paddle->prices->get($priceId);
        } catch (ApiError | MalformedResponse $e) {
            Log::error('Failed to get Paddle price', [
                'price_id' => $priceId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get all prices for a product.
     */
    public function getPricesForProduct(string $productId): array
    {
        if (!$this->paddle) {
            return [];
        }

        try {
            $operation = new ListPrices(productIds: [$productId]);
            $collection = $this->paddle->prices->list($operation);

            // Convert collection to array
            $prices = [];
            foreach ($collection as $price) {
                $prices[] = $price;
            }
            return $prices;
        } catch (ApiError | MalformedResponse $e) {
            Log::error('Failed to get Paddle prices for product', [
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Create a checkout session for a subscription.
     */
    public function createCheckoutSession(User $user, PricingPlan $plan, string $billingCycle = 'month'): ?array
    {
        // Handle development mode with placeholder credentials
        if ($this->isDevelopmentMode()) {
            return $this->createMockCheckoutSession($user, $plan, $billingCycle);
        }

        // Check if Paddle is properly configured
        if (!$this->isConfigured()) {
            Log::error('Paddle is not properly configured', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'has_paddle_client' => $this->paddle !== null,
                'config_debug' => [
                    'api_key_present' => !empty(config('paddle.api_key')),
                    'client_token_present' => !empty(config('paddle.client_token')),
                    'api_key_is_placeholder' => $this->isPlaceholderValue(config('paddle.api_key', '')),
                    'client_token_is_placeholder' => $this->isPlaceholderValue(config('paddle.client_token', '')),
                ]
            ]);
            return null;
        }

        try {
            $customer = $this->getOrCreateCustomer($user);
            if (!$customer) {
                Log::error('Failed to get or create Paddle customer', [
                    'user_id' => $user->id,
                    'plan_id' => $plan->id,
                    'is_configured' => $this->isConfigured(),
                    'is_development_mode' => $this->isDevelopmentMode(),
                ]);
                return null;
            }

            // Get the appropriate price ID based on billing cycle
            $priceId = $billingCycle === 'year'
                ? $plan->paddle_price_id_yearly
                : $plan->paddle_price_id_monthly;

            if (!$priceId) {
                Log::error('No Paddle price ID configured for plan', [
                    'plan_id' => $plan->id,
                    'billing_cycle' => $billingCycle
                ]);
                return null;
            }

            // Check if price ID is a placeholder - if so, provide helpful error
            if ($this->isPlaceholderValue($priceId)) {
                Log::error('Paddle price ID is a placeholder value', [
                    'plan_id' => $plan->id,
                    'billing_cycle' => $billingCycle,
                    'price_id' => $priceId,
                    'help' => 'Please configure real Paddle price IDs in your Paddle dashboard and update the pricing plan.'
                ]);

                // Return error information instead of null
                return [
                    'error' => 'placeholder_price_id',
                    'message' => 'Paddle checkout failed: The pricing plan uses placeholder price IDs.',
                    'help' => 'Create products in your Paddle dashboard and update the pricing plan with real price IDs.',
                    'price_id' => $priceId,
                    'plan_name' => $plan->name,
                    'development_mode' => true
                ];
            }

            // Create transaction for checkout
            $operation = new CreateTransaction(
                items: [
                    new TransactionCreateItem(
                        priceId: $priceId,
                        quantity: 1
                    )
                ],
                customerId: $customer->id
            );

            $transaction = $this->paddle->transactions->create($operation);

            Log::info('Created Paddle checkout session', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'transaction_id' => $transaction->id,
                'billing_cycle' => $billingCycle
            ]);

            return [
                'transaction_id' => $transaction->id,
                'checkout_url' => $transaction->checkout?->url,
                'customer_id' => $customer->id,
                'price_id' => $priceId,
            ];
        } catch (ApiError | MalformedResponse $e) {
            Log::error('Failed to create Paddle checkout session', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get transaction details.
     */
    public function getTransaction(string $transactionId): ?Transaction
    {
        if (!$this->paddle) {
            return null;
        }

        try {
            return $this->paddle->transactions->get($transactionId);
        } catch (ApiError | MalformedResponse $e) {
            Log::error('Failed to get Paddle transaction', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get subscription details.
     */
    public function getSubscription(string $subscriptionId): ?Subscription
    {
        if (!$this->paddle) {
            return null;
        }

        try {
            return $this->paddle->subscriptions->get($subscriptionId);
        } catch (ApiError | MalformedResponse $e) {
            Log::error('Failed to get Paddle subscription', [
                'subscription_id' => $subscriptionId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Verify webhook signature.
     */
    public function verifyWebhookSignature(string $payload, string $signature): bool
    {
        $secret = config('paddle.webhook_secret');
        if (!$secret) {
            Log::warning('Paddle webhook secret not configured');
            return false;
        }

        $expectedSignature = hash_hmac('sha256', $payload, $secret);
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Check if Paddle is properly configured.
     */
    public function isConfigured(): bool
    {
        $config = config('paddle');

        // Check if we have a valid Paddle client
        if ($this->paddle === null) {
            return false;
        }

        // Check if credentials are present and not placeholders
        if (empty($config['api_key']) || empty($config['client_token'])) {
            return false;
        }

        // Check if credentials are placeholder values
        if ($this->isPlaceholderValue($config['api_key']) || $this->isPlaceholderValue($config['client_token'])) {
            return false;
        }

        return true;
    }

    /**
     * Check if we're in development mode with placeholder credentials.
     */
    public function isDevelopmentMode(): bool
    {
        if (!config('app.debug')) {
            return false;
        }

        $config = config('paddle');
        return $this->isPlaceholderValue($config['api_key'] ?? '') ||
               $this->isPlaceholderValue($config['client_token'] ?? '');
    }

    /**
     * Check if a value is a placeholder (test/demo value).
     */
    public function isPlaceholderValue(string $value): bool
    {
        // Empty values are considered placeholders
        if (empty($value)) {
            return true;
        }

        $lowerValue = strtolower($value);

        // Check for obvious placeholder patterns (but not real Paddle test tokens)
        $placeholderPatterns = [
            'placeholder',
            'demo_',
            'example_',
            'your_',
            'replace_',
            'test_key',
            'test_token',
            'test_secret',
            // Common price ID patterns that are placeholders
            'pri_premium_monthly_',
            'pri_premium_yearly_',
            'pri_basic_monthly_',
            'pri_basic_yearly_',
            'pri_pro_monthly_',
            'pri_pro_yearly_',
        ];

        foreach ($placeholderPatterns as $pattern) {
            if (str_contains($lowerValue, $pattern)) {
                return true;
            }
        }

        // Check for generic test_ patterns that are too short or simple (but allow real Paddle tokens)
        if (preg_match('/^test_[a-z0-9]{1,10}$/i', $value)) {
            return true;
        }

        // Check if it's a simple numeric price ID (likely placeholder)
        if (preg_match('/^pri_\w+_\d+$/', $value)) {
            return true;
        }

        // Check for very short values that are likely placeholders
        if (strlen($value) < 10) {
            return true;
        }

        return false;
    }

    /**
     * Create a mock customer for development mode.
     */
    protected function createMockCustomer(User $user): ?Customer
    {
        $mockCustomerId = 'dev_cus_' . $user->id;

        Log::info('Mock Paddle customer (development mode)', [
            'user_id' => $user->id,
            'mock_customer_id' => $mockCustomerId,
            'note' => 'This is a mock customer for development. Configure real Paddle credentials for production.'
        ]);

        // For development mode, we'll return null and handle it in the checkout method
        // This avoids complex mock object creation
        return null;
    }

    /**
     * Create a mock checkout session for development mode.
     */
    protected function createMockCheckoutSession(User $user, PricingPlan $plan, string $billingCycle): array
    {
        $mockTransactionId = 'dev_txn_' . uniqid();
        $mockCustomerId = 'dev_cus_' . $user->id;
        $mockPriceId = 'dev_price_' . $plan->id . '_' . $billingCycle;

        Log::info('Created mock Paddle checkout session (development mode)', [
            'user_id' => $user->id,
            'plan_id' => $plan->id,
            'transaction_id' => $mockTransactionId,
            'billing_cycle' => $billingCycle,
            'note' => 'This is a mock session for development. Configure real Paddle credentials for production.'
        ]);

        return [
            'transaction_id' => $mockTransactionId,
            'checkout_url' => config('app.url') . '/paddle/mock-checkout?transaction=' . $mockTransactionId,
            'customer_id' => $mockCustomerId,
            'price_id' => $mockPriceId,
            'development_mode' => true,
        ];
    }

    /**
     * Get Paddle configuration for frontend.
     */
    public function getFrontendConfig(): array
    {
        $config = [
            'client_token' => config('paddle.client_token'),
            'environment' => config('paddle.environment'),
            'currency' => config('paddle.currency'),
        ];

        // Add development mode flag if using placeholder credentials
        if ($this->isDevelopmentMode()) {
            $config['development_mode'] = true;
            $config['note'] = 'Using placeholder credentials. Configure real Paddle credentials for production.';
            $config['mock_mode'] = true;

            // Log frontend config request in development mode
            if (config('app.debug')) {
                Log::info('Paddle frontend config requested (development mode)', [
                    'environment' => $config['environment'],
                    'has_client_token' => !empty($config['client_token']),
                    'client_token_is_placeholder' => $this->isPlaceholderValue($config['client_token'] ?? ''),
                ]);
            }
        }

        return $config;
    }
}
